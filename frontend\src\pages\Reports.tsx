import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { 
  BarChart3, 
  FileText, 
  Download, 
  ExternalLink,
  TrendingUp,
  PieChart,
  Calendar
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { reportsApi } from '@/services/api'
import { formatDate } from '@/lib/utils'

export function Reports() {
  const [selectedReport, setSelectedReport] = useState<'allure' | 'playwright' | 'logs'>('allure')

  const { data: allureReports = [] } = useQuery({
    queryKey: ['allure-reports'],
    queryFn: reportsApi.getAllure,
    enabled: selectedReport === 'allure',
  })

  const { data: logFiles = [] } = useQuery({
    queryKey: ['log-files'],
    queryFn: reportsApi.getLogs,
    enabled: selectedReport === 'logs',
  })

  const reportTypes = [
    {
      id: 'allure',
      name: 'Allure Reports',
      description: 'Detailed test execution reports with rich visualizations',
      icon: BarChart3,
      color: 'text-blue-600',
    },
    {
      id: 'playwright',
      name: 'Playwright Reports',
      description: 'Native Playwright HTML reports with traces and screenshots',
      icon: FileText,
      color: 'text-green-600',
    },
    {
      id: 'logs',
      name: 'Execution Logs',
      description: 'Raw execution logs and debugging information',
      icon: Calendar,
      color: 'text-purple-600',
    },
  ]

  const handleDownloadReport = (type: string) => {
    // Implementation for downloading reports
    console.log(`Downloading ${type} report`)
  }

  const handleViewReport = (type: string) => {
    if (type === 'playwright') {
      window.open('/api/reports/playwright', '_blank')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Reports</h1>
          <p className="text-muted-foreground">
            View test execution reports and analytics
          </p>
        </div>
        <Button onClick={() => handleDownloadReport(selectedReport)}>
          <Download className="mr-2 h-4 w-4" />
          Export Report
        </Button>
      </div>

      {/* Report Type Selector */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {reportTypes.map((type) => (
          <Card
            key={type.id}
            className={`cursor-pointer transition-all hover:shadow-md ${
              selectedReport === type.id ? 'ring-2 ring-primary' : ''
            }`}
            onClick={() => setSelectedReport(type.id as any)}
          >
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <type.icon className={`h-8 w-8 ${type.color}`} />
                <div className="flex-1">
                  <h3 className="font-semibold">{type.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {type.description}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Report Content */}
      <div className="space-y-6">
        {selectedReport === 'allure' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Allure Reports</h2>
              <Button variant="outline" onClick={() => handleViewReport('allure')}>
                <ExternalLink className="mr-2 h-4 w-4" />
                Open Allure Server
              </Button>
            </div>

            {allureReports.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <BarChart3 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Allure Reports</h3>
                  <p className="text-muted-foreground">
                    Run some tests to generate Allure reports
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {allureReports.slice(0, 6).map((report: any, index: number) => (
                  <Card key={index} className="hover:shadow-md transition-shadow">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-lg">{report.name || `Test ${index + 1}`}</CardTitle>
                        <Badge variant={report.status === 'passed' ? 'success' : 'destructive'}>
                          {report.status}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Duration:</span>
                        <span>{report.time?.duration || 0}ms</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Attachments:</span>
                        <span>{report.attachments?.length || 0}</span>
                      </div>
                      <Button size="sm" variant="outline" className="w-full mt-2">
                        <FileText className="h-3 w-3 mr-1" />
                        View Details
                      </Button>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {selectedReport === 'playwright' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Playwright Reports</h2>
              <Button onClick={() => handleViewReport('playwright')}>
                <ExternalLink className="mr-2 h-4 w-4" />
                Open HTML Report
              </Button>
            </div>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5" />
                  Latest HTML Report
                </CardTitle>
                <CardDescription>
                  Interactive Playwright test report with traces and screenshots
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                    <div className="p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-green-600">85%</div>
                      <div className="text-sm text-muted-foreground">Pass Rate</div>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <div className="text-2xl font-bold">142</div>
                      <div className="text-sm text-muted-foreground">Total Tests</div>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-green-600">121</div>
                      <div className="text-sm text-muted-foreground">Passed</div>
                    </div>
                    <div className="p-4 border rounded-lg">
                      <div className="text-2xl font-bold text-red-600">21</div>
                      <div className="text-sm text-muted-foreground">Failed</div>
                    </div>
                  </div>

                  <div className="flex justify-center">
                    <Button onClick={() => handleViewReport('playwright')}>
                      <ExternalLink className="mr-2 h-4 w-4" />
                      View Full Report
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {selectedReport === 'logs' && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Execution Logs</h2>

            {logFiles.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <Calendar className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Log Files</h3>
                  <p className="text-muted-foreground">
                    Execute some tests to generate log files
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-3">
                {logFiles.map((logFile: any, index: number) => (
                  <Card key={index} className="hover:shadow-sm transition-shadow">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium">{logFile.filename}</h4>
                          <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                            <span>Size: {(logFile.size / 1024).toFixed(1)} KB</span>
                            <span>Modified: {formatDate(new Date(logFile.modified))}</span>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            <FileText className="h-3 w-3 mr-1" />
                            View
                          </Button>
                          <Button size="sm" variant="outline">
                            <Download className="h-3 w-3 mr-1" />
                            Download
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Analytics Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="mr-2 h-5 w-5" />
            Test Analytics Summary
          </CardTitle>
          <CardDescription>
            Overview of test execution trends and metrics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">94.2%</div>
              <div className="text-sm text-muted-foreground">Average Pass Rate</div>
              <div className="text-xs text-muted-foreground mt-1">Last 30 days</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">2.3m</div>
              <div className="text-sm text-muted-foreground">Avg Execution Time</div>
              <div className="text-xs text-muted-foreground mt-1">Per test suite</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">1,247</div>
              <div className="text-sm text-muted-foreground">Total Executions</div>
              <div className="text-xs text-muted-foreground mt-1">This month</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

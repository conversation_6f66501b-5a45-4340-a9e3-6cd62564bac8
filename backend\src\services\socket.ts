import { Server, Socket } from 'socket.io'
import { logger } from '../utils/logger.js'
import type { TestExecutionService } from './testExecution.js'

export function setupSocketHandlers(io: Server, testExecution: TestExecutionService) {
  io.on('connection', (socket: Socket) => {
    logger.info(`Client connected: ${socket.id}`)

    // Join rooms for real-time updates
    socket.on('join-execution', (executionId: string) => {
      socket.join(`execution-${executionId}`)
      logger.info(`Client ${socket.id} joined execution room: ${executionId}`)
    })

    socket.on('leave-execution', (executionId: string) => {
      socket.leave(`execution-${executionId}`)
      logger.info(`Client ${socket.id} left execution room: ${executionId}`)
    })

    // Join dashboard room for general updates
    socket.on('join-dashboard', () => {
      socket.join('dashboard')
      logger.info(`Client ${socket.id} joined dashboard room`)
    })

    socket.on('leave-dashboard', () => {
      socket.leave('dashboard')
      logger.info(`Client ${socket.id} left dashboard room`)
    })

    // Handle test execution requests
    socket.on('start-test-execution', async (data: {
      tests?: string[]
      suites?: string[]
      projects?: string[]
      command?: string
    }) => {
      try {
        logger.info(`Test execution requested by ${socket.id}:`, data)
        const execution = await testExecution.startExecution(data)
        socket.emit('execution-started', execution)
      } catch (error) {
        logger.error('Failed to start test execution:', error)
        socket.emit('execution-error', {
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    })

    // Handle execution cancellation
    socket.on('cancel-execution', async (executionId: string) => {
      try {
        logger.info(`Execution cancellation requested by ${socket.id}: ${executionId}`)
        await testExecution.cancelExecution(executionId)
        socket.emit('execution-cancelled', { executionId })
      } catch (error) {
        logger.error('Failed to cancel execution:', error)
        socket.emit('execution-error', {
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    })

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      logger.info(`Client disconnected: ${socket.id}, reason: ${reason}`)
    })

    // Send initial connection confirmation
    socket.emit('connected', {
      id: socket.id,
      timestamp: new Date().toISOString()
    })
  })

  // Broadcast system-wide events
  setInterval(() => {
    io.to('dashboard').emit('system-stats', {
      timestamp: new Date().toISOString(),
      activeConnections: io.engine.clientsCount,
      activeExecutions: testExecution.getActiveExecutions().length,
      queuedExecutions: testExecution.getQueuedExecutions().length
    })
  }, 5000) // Every 5 seconds
}

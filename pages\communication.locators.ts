/**
 * Communication module locators
 * Contains all selectors and locators for the Communication page
 */

export const communicationLocators = {
  // Navigation
  navigation: {
    communicationMenu: 'text=Communication',
    messageGroupsLink: 'link[name="Message Groups"]',
    bulkCommunicationsLink: 'link[name="Bulk Communications"]',
    triggeredCommunicationsLink: 'link[name="Triggered Communications"]',
    scheduledCommunicationsLink: 'link[name="Scheduled Communications"]',
    surveyCommunicationsLink: 'link[name="Survey Communications"]'
  },

  // Message groups
  messageGroups: {
    dataGrid: '#enrol-table tbody tr',
    searchBar: '//input[@id=\'search-enrol\']',
    applyLink: 'link[name="Apply"]',
    backNavigation: 'cell[name=" Message Group"]'
  },

  // Buttons
  buttons: {
    addNewUserGroup: 'button[name="Add New User Group"]',
    setupCommunication: 'button[name="Setup Communication"]'
  },

  // Form fields
  forms: {
    groupNameField: 'input[aria-label="Give your group a name:"]'
  },

  // Headings and cells
  headings: {
    bulkCommunicationsSetup: 'heading[name="Bulk Communications Setup"]',
    triggeredCommunications: 'heading[name="Triggered Communications"]',
    scheduledCommunications: 'heading[name="Scheduled Communications"]'
  },

  // Message components
  message: {
    nameCell: 'cell[name="Message Name"]'
  }
};

/**
 * Communication page role-based locators for better accessibility
 */
export const communicationRoleLocators = {
  communicationMenu: { text: 'Communication', exact: true },
  messageGroupsLink: { role: 'link', name: 'Message Groups' },
  bulkCommunicationsLink: { role: 'link', name: 'Bulk Communications' },
  triggeredCommunicationsLink: { role: 'link', name: 'Triggered Communications' },
  scheduledCommunicationsLink: { role: 'link', name: 'Scheduled Communications' },
  surveyCommunicationsLink: { role: 'link', name: 'Survey Communications' },
  addNewUserGroupButton: { role: 'button', name: 'Add New User Group' },
  setupCommunicationButton: { role: 'button', name: 'Setup Communication' },
  groupNameField: { label: 'Give your group a name:' },
  applyLink: { role: 'link', name: 'Apply' },
  backNavigationCell: { role: 'cell', name: ' Message Group' },
  bulkCommunicationsHeading: { role: 'heading', name: 'Bulk Communications Setup' },
  triggeredCommunicationsHeading: { role: 'heading', name: 'Triggered Communications' },
  scheduledCommunicationsHeading: { role: 'heading', name: 'Scheduled Communications' },
  messageNameCell: { role: 'cell', name: 'Message Name', exact: true }
};

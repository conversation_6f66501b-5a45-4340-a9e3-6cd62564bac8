{"name": "inconnect-test-management-backend", "version": "1.0.0", "description": "Backend API for INCONNECT Test Management System", "main": "dist/index.js", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "type-check": "tsc --noEmit"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.4", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "chokidar": "^3.5.3", "node-cron": "^3.0.3", "uuid": "^9.0.1", "zod": "^3.22.4", "winston": "^3.11.0", "multer": "^1.4.5-lts.1", "archiver": "^6.0.1", "fast-glob": "^3.3.2", "@typescript-eslint/typescript-estree": "^6.12.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/node": "^20.9.0", "@types/uuid": "^9.0.7", "@types/multer": "^1.4.11", "@types/archiver": "^6.0.2", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "tsx": "^4.6.0", "typescript": "^5.3.2"}}
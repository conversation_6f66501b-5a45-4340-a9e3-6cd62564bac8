import { ReactNode } from 'react'
import { Link, useLocation } from 'react-router-dom'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useSocketStore } from '@/stores/socketStore'
import {
  LayoutDashboard,
  TestTube,
  Play,
  Calendar,
  BarChart3,
  Settings,
  Wifi,
  WifiOff,
} from 'lucide-react'

interface LayoutProps {
  children: ReactNode
}

const navigation = [
  { name: 'Dashboard', href: '/', icon: LayoutDashboard },
  { name: 'Test Suites', href: '/test-suites', icon: TestTube },
  { name: 'Execution', href: '/execution', icon: Play },
  { name: 'Test Plans', href: '/test-plans', icon: Calendar },
  { name: 'Reports', href: '/reports', icon: BarChart3 },
  { name: 'Settings', href: '/settings', icon: Settings },
]

export function Layout({ children }: LayoutProps) {
  const location = useLocation()
  const { isConnected, activeExecutions } = useSocketStore()

  return (
    <div className="min-h-screen bg-background">
      {/* Sidebar */}
      <div className="fixed inset-y-0 left-0 z-50 w-64 bg-card border-r">
        <div className="flex h-16 items-center px-6 border-b">
          <div className="flex items-center space-x-2">
            <TestTube className="h-8 w-8 text-primary" />
            <div>
              <h1 className="text-lg font-semibold">INCONNECT</h1>
              <p className="text-xs text-muted-foreground">Test Management</p>
            </div>
          </div>
        </div>

        <nav className="flex-1 px-4 py-6 space-y-2">
          {navigation.map((item) => {
            const isActive = location.pathname === item.href
            return (
              <Link
                key={item.name}
                to={item.href}
                className={cn(
                  'flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors',
                  isActive
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                )}
              >
                <item.icon className="mr-3 h-4 w-4" />
                {item.name}
                {item.name === 'Execution' && activeExecutions.length > 0 && (
                  <Badge variant="destructive" className="ml-auto">
                    {activeExecutions.length}
                  </Badge>
                )}
              </Link>
            )
          })}
        </nav>

        {/* Connection Status */}
        <div className="p-4 border-t">
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <>
                <Wifi className="h-4 w-4 text-green-500" />
                <span className="text-sm text-green-600">Connected</span>
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 text-red-500" />
                <span className="text-sm text-red-600">Disconnected</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="pl-64">
        <header className="h-16 bg-card border-b flex items-center justify-between px-6">
          <div>
            <h2 className="text-xl font-semibold capitalize">
              {location.pathname === '/' ? 'Dashboard' : location.pathname.slice(1).replace('-', ' ')}
            </h2>
          </div>
          
          <div className="flex items-center space-x-4">
            {activeExecutions.length > 0 && (
              <Badge variant="outline" className="animate-pulse">
                {activeExecutions.length} Running
              </Badge>
            )}
            
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <div className={cn(
                "h-2 w-2 rounded-full",
                isConnected ? "bg-green-500" : "bg-red-500"
              )} />
              <span>{isConnected ? 'Online' : 'Offline'}</span>
            </div>
          </div>
        </header>

        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  )
}

import { useState } from 'react'
import { Plus, Calendar, Play, Edit, Trash2, Clock } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatDate } from '@/lib/utils'

// Mock data for test plans
const mockTestPlans = [
  {
    id: '1',
    name: 'Nightly Regression Suite',
    description: 'Complete regression testing for all approved tests',
    tests: ['test1', 'test2', 'test3'],
    suites: ['approved_tests'],
    projects: ['approved-tests'],
    schedule: {
      enabled: true,
      cron: '0 2 * * *',
      timezone: 'UTC'
    },
    status: 'active',
    createdAt: new Date('2024-01-15'),
    lastRun: new Date('2024-01-20')
  },
  {
    id: '2',
    name: 'Smoke Tests',
    description: 'Quick smoke tests for critical functionality',
    tests: ['test4', 'test5'],
    suites: ['critical_tests'],
    projects: ['bot-user-tests'],
    schedule: {
      enabled: false,
      cron: '0 */4 * * *',
      timezone: 'UTC'
    },
    status: 'inactive',
    createdAt: new Date('2024-01-10'),
    lastRun: new Date('2024-01-19')
  }
]

export function TestPlans() {
  const [testPlans] = useState(mockTestPlans)
  const [showCreateForm, setShowCreateForm] = useState(false)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Test Plans</h1>
          <p className="text-muted-foreground">
            Organize and schedule your test executions
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Test Plan
        </Button>
      </div>

      {/* Create Form */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>Create New Test Plan</CardTitle>
            <CardDescription>
              Define a collection of tests to run together
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Plan Name</label>
              <input
                type="text"
                placeholder="Enter test plan name"
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Description</label>
              <textarea
                placeholder="Describe what this test plan covers"
                rows={3}
                className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Projects</label>
                <select className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                  <option>approved-tests</option>
                  <option>not-approved-tests</option>
                  <option>bot-user-tests</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-2">Schedule</label>
                <select className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                  <option>Manual only</option>
                  <option>Daily at 2 AM</option>
                  <option>Every 4 hours</option>
                  <option>Weekly</option>
                </select>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                Cancel
              </Button>
              <Button>Create Plan</Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Test Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {testPlans.map((plan) => (
          <Card key={plan.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg">{plan.name}</CardTitle>
                  <CardDescription className="mt-1">
                    {plan.description}
                  </CardDescription>
                </div>
                <Badge variant={plan.status === 'active' ? 'success' : 'secondary'}>
                  {plan.status}
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Tests:</span>
                  <span className="font-medium">{plan.tests.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Projects:</span>
                  <span className="font-medium">{plan.projects.join(', ')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Created:</span>
                  <span className="font-medium">{formatDate(plan.createdAt)}</span>
                </div>
                {plan.lastRun && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Last Run:</span>
                    <span className="font-medium">{formatDate(plan.lastRun)}</span>
                  </div>
                )}
              </div>

              {plan.schedule.enabled && (
                <div className="flex items-center space-x-2 text-sm">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">
                    Scheduled: {plan.schedule.cron}
                  </span>
                </div>
              )}

              <div className="flex items-center justify-between pt-2 border-t">
                <div className="flex space-x-2">
                  <Button size="sm" variant="outline">
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  <Button size="sm" variant="outline">
                    <Trash2 className="h-3 w-3 mr-1" />
                    Delete
                  </Button>
                </div>
                <Button size="sm">
                  <Play className="h-3 w-3 mr-1" />
                  Run
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {testPlans.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Calendar className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Test Plans</h3>
            <p className="text-muted-foreground mb-4">
              Create your first test plan to organize and schedule test executions
            </p>
            <Button onClick={() => setShowCreateForm(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create Test Plan
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

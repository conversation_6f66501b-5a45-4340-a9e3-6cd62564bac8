import { Routes, Route } from 'react-router-dom'
import { useEffect } from 'react'
import { Layout } from './components/Layout'
import { Dashboard } from './pages/Dashboard'
import { TestSuites } from './pages/TestSuites'
import { TestExecution } from './pages/TestExecution'
import { TestPlans } from './pages/TestPlans'
import { Reports } from './pages/Reports'
import { Settings } from './pages/Settings'
import { useSocketStore } from './stores/socketStore'

function App() {
  const { connect, disconnect } = useSocketStore()

  useEffect(() => {
    // Connect to WebSocket on app start
    connect()

    // Cleanup on unmount
    return () => {
      disconnect()
    }
  }, [connect, disconnect])

  return (
    <Layout>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/test-suites" element={<TestSuites />} />
        <Route path="/execution" element={<TestExecution />} />
        <Route path="/test-plans" element={<TestPlans />} />
        <Route path="/reports" element={<Reports />} />
        <Route path="/settings" element={<Settings />} />
      </Routes>
    </Layout>
  )
}

export default App

import { useQuery } from '@tanstack/react-query'
import { useEffect } from 'react'
import { 
  TestTube, 
  Play, 
  CheckCircle, 
  XCircle, 
  Clock,
  TrendingUp,
  Activity
} from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { testsApi, executionApi } from '@/services/api'
import { useSocketStore } from '@/stores/socketStore'
import { formatDuration, formatDate } from '@/lib/utils'

export function Dashboard() {
  const { joinDashboard, leaveDashboard, activeExecutions } = useSocketStore()

  const { data: stats } = useQuery({
    queryKey: ['test-stats'],
    queryFn: testsApi.getStats,
    refetchInterval: 30000, // Refresh every 30 seconds
  })

  const { data: recentExecutions = [] } = useQuery({
    queryKey: ['recent-executions'],
    queryFn: () => executionApi.getAll({ limit: 5 }),
    refetchInterval: 10000, // Refresh every 10 seconds
  })

  useEffect(() => {
    joinDashboard()
    return () => leaveDashboard()
  }, [joinDashboard, leaveDashboard])

  const statCards = [
    {
      title: 'Total Tests',
      value: stats?.totalTests || 0,
      icon: TestTube,
      description: 'Across all test suites',
      color: 'text-blue-600',
    },
    {
      title: 'Test Suites',
      value: stats?.totalSuites || 0,
      icon: Activity,
      description: 'Organized test collections',
      color: 'text-purple-600',
    },
    {
      title: 'Pass Rate',
      value: `${(stats?.passRate || 0).toFixed(1)}%`,
      icon: TrendingUp,
      description: 'Overall success rate',
      color: 'text-green-600',
    },
    {
      title: 'Active Executions',
      value: activeExecutions.length,
      icon: Play,
      description: 'Currently running',
      color: 'text-orange-600',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <p className="text-muted-foreground">
          Overview of your test automation system
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Active Executions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Play className="mr-2 h-5 w-5" />
              Active Executions
            </CardTitle>
            <CardDescription>
              Tests currently running
            </CardDescription>
          </CardHeader>
          <CardContent>
            {activeExecutions.length === 0 ? (
              <div className="text-center py-8">
                <Clock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No active executions</p>
              </div>
            ) : (
              <div className="space-y-4">
                {activeExecutions.map((execution) => (
                  <div
                    key={execution.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="font-medium">
                        {execution.id.slice(0, 8)}...
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {execution.project}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="animate-pulse">
                        {execution.status}
                      </Badge>
                      <div className="text-sm text-muted-foreground">
                        {formatDuration(Date.now() - execution.startTime.getTime())}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Recent Executions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="mr-2 h-5 w-5" />
              Recent Executions
            </CardTitle>
            <CardDescription>
              Latest test runs
            </CardDescription>
          </CardHeader>
          <CardContent>
            {recentExecutions.length === 0 ? (
              <div className="text-center py-8">
                <TestTube className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No recent executions</p>
              </div>
            ) : (
              <div className="space-y-4">
                {recentExecutions.map((execution) => (
                  <div
                    key={execution.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex-1">
                      <div className="font-medium">
                        {execution.id.slice(0, 8)}...
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {execution.project} • {formatDate(execution.startTime)}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {execution.status === 'completed' && (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      )}
                      {execution.status === 'failed' && (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      {execution.status === 'cancelled' && (
                        <Clock className="h-4 w-4 text-yellow-600" />
                      )}
                      <Badge 
                        variant={
                          execution.status === 'completed' ? 'success' :
                          execution.status === 'failed' ? 'destructive' :
                          'secondary'
                        }
                      >
                        {execution.status}
                      </Badge>
                      {execution.duration && (
                        <div className="text-sm text-muted-foreground">
                          {formatDuration(execution.duration)}
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks and shortcuts
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button className="h-20 flex-col space-y-2">
              <TestTube className="h-6 w-6" />
              <span>Browse Test Suites</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <Play className="h-6 w-6" />
              <span>Start Execution</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col space-y-2">
              <Activity className="h-6 w-6" />
              <span>View Reports</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

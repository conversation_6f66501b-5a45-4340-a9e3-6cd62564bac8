import fs from 'fs/promises'
import path from 'path'
import glob from 'fast-glob'
import { parse } from '@typescript-eslint/typescript-estree'
import { logger } from '../utils/logger.js'
import type { TestSuite, Test, PlaywrightConfig, SystemStats } from '../types/index.js'

export class TestDiscoveryService {
  private suites: Map<string, TestSuite> = new Map()
  private tests: Map<string, Test> = new Map()
  private config: PlaywrightConfig | null = null
  private lastScan: Date | null = null
  private readonly projectRoot: string

  constructor() {
    this.projectRoot = path.join(process.cwd(), '..')
  }

  async initialize(): Promise<void> {
    logger.info('Initializing test discovery service...')
    await this.loadPlaywrightConfig()
    await this.scanTestFiles()
    logger.info(`Test discovery initialized: ${this.suites.size} suites, ${this.tests.size} tests`)
  }

  async refresh(): Promise<void> {
    logger.info('Refreshing test discovery...')
    this.suites.clear()
    this.tests.clear()
    await this.loadPlaywrightConfig()
    await this.scanTestFiles()
    this.lastScan = new Date()
    logger.info(`Test discovery refreshed: ${this.suites.size} suites, ${this.tests.size} tests`)
  }

  private async loadPlaywrightConfig(): Promise<void> {
    try {
      const configPath = path.join(this.projectRoot, 'playwright.config.ts')
      const configExists = await fs.access(configPath).then(() => true).catch(() => false)
      
      if (!configExists) {
        logger.warn('Playwright config not found, using defaults')
        this.config = this.getDefaultConfig()
        return
      }

      // For now, use a mock config based on your actual config
      // In production, you'd want to actually parse the TypeScript file
      this.config = {
        testDir: './tests',
        projects: [
          {
            name: 'bot-user-tests',
            testMatch: ['**/*.spec.ts'],
            testIgnore: ['**/*.setup.ts'],
            use: {
              headless: true,
              storageState: 'playwright/.auth/bot-user.json'
            }
          },
          {
            name: 'helper-bot-tests',
            testMatch: ['**/*.spec.ts'],
            testIgnore: ['**/*.setup.ts'],
            use: {
              headless: true,
              storageState: 'playwright/.auth/helper-bot-user.json'
            }
          },
          {
            name: 'approved-tests',
            testMatch: ['approved_tests/**/*.spec.ts'],
            testIgnore: ['**/*.setup.ts'],
            use: {
              headless: true,
              storageState: 'playwright/.auth/bot-user.json'
            }
          },
          {
            name: 'not-approved-tests',
            testMatch: ['not_approved/**/*.spec.ts'],
            testIgnore: ['**/*.setup.ts'],
            use: {
              headless: true,
              storageState: 'playwright/.auth/bot-user.json'
            }
          }
        ],
        reporter: [['line'], ['allure-playwright'], ['html']],
        timeout: 250000,
        workers: 2,
        retries: 0
      }

      logger.info('Playwright configuration loaded')
    } catch (error) {
      logger.error('Failed to load Playwright config:', error)
      this.config = this.getDefaultConfig()
    }
  }

  private getDefaultConfig(): PlaywrightConfig {
    return {
      testDir: './tests',
      projects: [
        {
          name: 'default',
          testMatch: ['**/*.spec.ts'],
          use: { headless: true }
        }
      ],
      reporter: [['line']],
      timeout: 30000,
      workers: 1,
      retries: 0
    }
  }

  private async scanTestFiles(): Promise<void> {
    try {
      const testDir = path.join(this.projectRoot, this.config?.testDir || './tests')
      const testFiles = await glob('**/*.spec.ts', {
        cwd: testDir,
        absolute: true
      })

      logger.info(`Found ${testFiles.length} test files`)

      for (const filePath of testFiles) {
        await this.parseTestFile(filePath)
      }
    } catch (error) {
      logger.error('Failed to scan test files:', error)
    }
  }

  private async parseTestFile(filePath: string): Promise<void> {
    try {
      const content = await fs.readFile(filePath, 'utf-8')
      const relativePath = path.relative(this.projectRoot, filePath)
      
      // Parse TypeScript AST
      const ast = parse(content, {
        loc: true,
        range: true,
        comment: true,
        tokens: true
      })

      const suites: TestSuite[] = []
      const tests: Test[] = []

      // Extract test suites and tests from AST
      await this.extractTestsFromAST(ast, filePath, relativePath, suites, tests, content)

      // Store suites and tests
      for (const suite of suites) {
        this.suites.set(suite.id, suite)
      }
      
      for (const test of tests) {
        this.tests.set(test.id, test)
      }

    } catch (error) {
      logger.error(`Failed to parse test file ${filePath}:`, error)
    }
  }

  private async extractTestsFromAST(
    ast: any,
    filePath: string,
    relativePath: string,
    suites: TestSuite[],
    tests: Test[],
    content: string
  ): Promise<void> {
    // This is a simplified parser - in production you'd want more robust AST traversal
    const lines = content.split('\n')

    // Find test.describe blocks
    const describeRegex = /test\.describe\(['"`]([^'"`]+)['"`]/g
    const testRegex = /test\(['"`]([^'"`]+)['"`]/g
    const testSkipRegex = /test\.skip\(['"`]([^'"`]+)['"`]/g

    let match
    let currentSuite: TestSuite | null = null

    // Extract test suites
    while ((match = describeRegex.exec(content)) !== null) {
      const suiteName = match[1]
      const lineNumber = content.substring(0, match.index).split('\n').length
      
      const suite: TestSuite = {
        id: `${relativePath}:${suiteName}`,
        name: suiteName,
        filePath: relativePath,
        tests: [],
        project: this.getProjectForFile(relativePath),
        tags: this.extractTags(content, match.index),
        status: 'passed' // Default status
      }

      suites.push(suite)
      currentSuite = suite
    }

    // Extract individual tests
    const allTestRegex = /test(?:\.skip)?\(['"`]([^'"`]+)['"`]/g
    while ((match = allTestRegex.exec(content)) !== null) {
      const testName = match[1]
      const lineNumber = content.substring(0, match.index).split('\n').length
      const isSkipped = match[0].includes('.skip')
      
      const test: Test = {
        id: `${relativePath}:${testName}`,
        name: testName,
        filePath: relativePath,
        line: lineNumber,
        suite: currentSuite?.name || 'Unknown',
        project: this.getProjectForFile(relativePath),
        tags: this.extractTags(content, match.index),
        severity: this.extractSeverity(content, match.index),
        status: isSkipped ? 'skipped' : 'passed'
      }

      tests.push(test)
      
      if (currentSuite) {
        currentSuite.tests.push(test)
      }
    }
  }

  private getProjectForFile(filePath: string): string {
    if (filePath.includes('approved_tests')) return 'approved-tests'
    if (filePath.includes('not_approved')) return 'not-approved-tests'
    return 'default'
  }

  private extractTags(content: string, position: number): string[] {
    // Extract tags from comments or annotations near the test
    const tags: string[] = []
    const beforeContent = content.substring(Math.max(0, position - 500), position)
    
    // Look for @tag annotations
    const tagRegex = /@(\w+)/g
    let match
    while ((match = tagRegex.exec(beforeContent)) !== null) {
      tags.push(match[1])
    }
    
    return tags
  }

  private extractSeverity(content: string, position: number): Test['severity'] {
    const beforeContent = content.substring(Math.max(0, position - 500), position)
    
    if (beforeContent.includes('Severity.BLOCKER')) return 'blocker'
    if (beforeContent.includes('Severity.CRITICAL')) return 'critical'
    if (beforeContent.includes('Severity.NORMAL')) return 'normal'
    if (beforeContent.includes('Severity.MINOR')) return 'minor'
    if (beforeContent.includes('Severity.TRIVIAL')) return 'trivial'
    
    return 'normal'
  }

  // Public API methods
  async getAllSuites(): Promise<TestSuite[]> {
    return Array.from(this.suites.values())
  }

  async getSuite(id: string): Promise<TestSuite | null> {
    return this.suites.get(id) || null
  }

  async getAllTests(filters?: {
    project?: string
    suite?: string
    status?: string
  }): Promise<Test[]> {
    let tests = Array.from(this.tests.values())
    
    if (filters?.project) {
      tests = tests.filter(test => test.project === filters.project)
    }
    
    if (filters?.suite) {
      tests = tests.filter(test => test.suite === filters.suite)
    }
    
    if (filters?.status) {
      tests = tests.filter(test => test.status === filters.status)
    }
    
    return tests
  }

  async getTest(id: string): Promise<Test | null> {
    return this.tests.get(id) || null
  }

  async searchTests(query: string, filters?: {
    project?: string
    suite?: string
  }): Promise<Test[]> {
    const allTests = await this.getAllTests(filters)
    const lowerQuery = query.toLowerCase()
    
    return allTests.filter(test => 
      test.name.toLowerCase().includes(lowerQuery) ||
      test.description?.toLowerCase().includes(lowerQuery) ||
      test.suite.toLowerCase().includes(lowerQuery) ||
      test.tags?.some(tag => tag.toLowerCase().includes(lowerQuery))
    )
  }

  async getStats(): Promise<SystemStats> {
    const tests = Array.from(this.tests.values())
    const passedTests = tests.filter(t => t.status === 'passed').length
    const totalTests = tests.length
    
    return {
      totalTests,
      totalSuites: this.suites.size,
      passRate: totalTests > 0 ? (passedTests / totalTests) * 100 : 0,
      avgDuration: 0, // Would calculate from execution history
      lastRunTime: this.lastScan || new Date(),
      activeExecutions: 0,
      queuedExecutions: 0
    }
  }
}

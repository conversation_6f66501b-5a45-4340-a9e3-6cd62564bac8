/**
 * Enrolment Management module locators
 * Contains all selectors and locators for the Enrolment Management page
 */

export const enrolmentManagementLocators = {
  // Navigation
  navigation: {
    enrolmentsMenu: 'text=Enrolments',
    enrolmentManagementLink: 'a:has-text("Enrolment Management")'
  },

  // Toggles and filters
  filters: {
    distanceToggle: '//form[@id=\'EnrolmentManagement\']/div[@class=\'col-xs-12\']/div[1]/div[1]/span[2]//label/span',
    reEnrolToggle: '//form[@id=\'EnrolmentManagement\']/div[@class=\'col-xs-12\']/div[1]/div[1]/span[3]//label/span',
    yearFilterButton: '#c4',
    customSelectDropdown: '.customselect-dropdown',
    yearDropdownMenu: '//div[@id=\'c4_DropdownMenu\']/div/div[10]/div[@class=\'form-group\']/div[@class=\'customselect-dropdown form-control\']',
    allCheckboxSelector: '#c22_all_ctl',
    yearCheckbox: 'input[type="checkbox"][id*="_opt_ctl"][name="c22"] + label',
    applyButton: 'button[name="APPLY"]',
    activeStatusButton: 'text=Active',
    enrolledStatusButton: '#c30'
  },

  // Data grid
  dataGrid: {
    enrolmentRows: '//*[@id="c41_dataGrid"]/table/tbody/tr',
    dataGridSelector: 'c8_dataGrid'
  },

  // Tabs
  tabs: {
    documentsTab: 'a[href="#documents"]',
    notesTab: 'link[name="Notes"]',
    changeDetailsTab: 'link[name="Change Details"]',
    financesTab: 'a.finaces-tab-link',
    paymentPlansTab: 'tab[name="Payment Plans"]'
  },

  // Document upload
  documents: {
    uploadButtons: 'button.btn.fullWidth.pull-right',
    uploadModal: 'h4.modal-title#SupportingDocumentsModalLabel'
  },

  // Notes
  notes: {
    newNoteButton: 'button[name=" New Note"]',
    noteInput: 'input[aria-label="Note"]',
    saveButton: 'text=Save',
    deleteButton: 'text=Delete',
    yesButton: 'button[name="Yes"]',
    noteCell: 'cell'
  },

  // Payment and generation
  payment: {
    generateProformaButton: 'button[name="Generate Proforma"]',
    generateProformaInvoiceText: 'text=Generate Proforma Invoice',
    paymentTypeSelect: 'select[aria-label="Enrolment Payment Type"]',
    paymentMethodSelect: 'select[aria-label="Enrolment Payment Method"]',
    downloadButton: 'text=Download',
    generateCOEButton: 'button[name="Generate COE"]',
    enrolmentLetterButton: 'button[name="Enrolment Letter"]',
    contractTypeDropdown: '#c152',
    generatePaymentPlanButton: 'button[name="Generate Payment plan & Milestones"]',
    confirmButton: 'button#c176',
    okButton: '.swal-button--confirm'
  },

  // Success messages
  messages: {
    successTitle: '.swal-title',
    successContent: '.swal-content p'
  },

  // Student search
  search: {
    individualLink: 'text=Individual',
    searchBox: '#c54_searchBox',
    studentDataGrid: '#c54_dataGrid',
    studentRow: 'tr',
    studentNameSelector: 'div.col-md-8'
  },

  // Payment plans
  paymentPlans: {
    dataGrid: '#c87_dataGrid'
  }
};

/**
 * Enrolment Management page role-based locators for better accessibility
 */
export const enrolmentManagementRoleLocators = {
  applyButton: { role: 'button', name: 'APPLY' },
  notesTab: { role: 'link', name: 'Notes' },
  changeDetailsTab: { role: 'link', name: 'Change Details' },
  newNoteButton: { role: 'button', name: ' New Note' },
  noteInput: { label: 'Note', exact: true },
  yesButton: { role: 'button', name: 'Yes' },
  generateProformaButton: { role: 'button', name: 'Generate Proforma' },
  generateCOEButton: { role: 'button', name: 'Generate COE' },
  enrolmentLetterButton: { role: 'button', name: 'Enrolment Letter' },
  generatePaymentPlanButton: { role: 'button', name: 'Generate Payment plan & Milestones' },
  paymentPlansTab: { role: 'tab', name: 'Payment Plans' },
  noteCell: { role: 'cell' },
  downloadText: { text: 'Download', exact: true }
};

/**
 * URL configurations for different environments
 */
export const enrolmentUrls = {
  enrolmentManagement: {
    accp: 'https://inconnect-accp-operations.stratusolvecloud.com/App/SalesAgent/EnrolmentManagement/',
    prod: 'https://operations.inscape.co.za/App/SalesAgent/EnrolmentManagement/'
  },
  login: {
    accp: 'https://inconnect-accp-connect.stratusolvecloud.com/UserManagement/login/',
    prod: 'https://connect.inscape.co.za/UserManagement/login/'
  }
};

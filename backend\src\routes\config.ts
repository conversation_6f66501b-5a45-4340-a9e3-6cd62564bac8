import { Router } from 'express'
import { logger } from '../utils/logger.js'
import path from 'path'
import fs from 'fs/promises'

export const configRoutes = Router()

// Get Playwright configuration
configRoutes.get('/playwright', async (req, res) => {
  try {
    const configPath = path.join(process.cwd(), '..', 'playwright.config.ts')
    const configExists = await fs.access(configPath).then(() => true).catch(() => false)
    
    if (!configExists) {
      return res.status(404).json({ error: 'Playwright config not found' })
    }
    
    // For now, return a simplified version
    // In a real implementation, you'd parse the TypeScript config
    const mockConfig = {
      testDir: './tests',
      projects: [
        {
          name: 'bot-user-tests',
          testMatch: /.*\.spec\.ts/,
          use: {
            headless: true,
            storageState: 'playwright/.auth/bot-user.json'
          }
        },
        {
          name: 'helper-bot-tests',
          testMatch: /.*\.spec\.ts/,
          use: {
            headless: true,
            storageState: 'playwright/.auth/helper-bot-user.json'
          }
        },
        {
          name: 'approved-tests',
          testMatch: /approved_tests\/.*\.spec\.ts/,
          use: {
            headless: true,
            storageState: 'playwright/.auth/bot-user.json'
          }
        },
        {
          name: 'not-approved-tests',
          testMatch: /not_approved\/.*\.spec\.ts/,
          use: {
            headless: true,
            storageState: 'playwright/.auth/bot-user.json'
          }
        }
      ],
      reporter: [
        ['line'],
        ['allure-playwright'],
        ['html']
      ],
      timeout: 250000,
      workers: 2,
      retries: 0
    }
    
    res.json(mockConfig)
  } catch (error) {
    logger.error('Failed to get Playwright config:', error)
    res.status(500).json({
      error: 'Failed to retrieve Playwright configuration',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get system information
configRoutes.get('/system', async (req, res) => {
  try {
    const systemInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      env: process.env.NODE_ENV || 'development',
      cwd: process.cwd(),
      timestamp: new Date().toISOString()
    }
    
    res.json(systemInfo)
  } catch (error) {
    logger.error('Failed to get system info:', error)
    res.status(500).json({
      error: 'Failed to retrieve system information',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get available test projects
configRoutes.get('/projects', async (req, res) => {
  try {
    // This would normally parse the actual config file
    const projects = [
      {
        name: 'bot-user-tests',
        description: 'Tests using bot user authentication',
        testMatch: ['**/*.spec.ts'],
        testIgnore: ['**/auth.setup.ts']
      },
      {
        name: 'helper-bot-tests',
        description: 'Tests using helper bot user authentication',
        testMatch: ['**/*.spec.ts'],
        testIgnore: ['**/auth.setup.ts']
      },
      {
        name: 'approved-tests',
        description: 'Production-ready approved tests',
        testMatch: ['approved_tests/**/*.spec.ts'],
        testIgnore: ['**/*.setup.ts']
      },
      {
        name: 'not-approved-tests',
        description: 'Tests under review',
        testMatch: ['not_approved/**/*.spec.ts'],
        testIgnore: ['**/*.setup.ts']
      }
    ]
    
    res.json(projects)
  } catch (error) {
    logger.error('Failed to get projects:', error)
    res.status(500).json({
      error: 'Failed to retrieve test projects',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

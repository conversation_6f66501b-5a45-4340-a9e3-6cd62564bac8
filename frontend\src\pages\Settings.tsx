import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { 
  Settings as SettingsIcon, 
  Server, 
  Database, 
  Bell,
  Shield,
  Palette,
  Save,
  RefreshCw
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { configApi, healthApi } from '@/services/api'
import { formatDate } from '@/lib/utils'

export function Settings() {
  const [activeTab, setActiveTab] = useState('system')

  const { data: systemInfo } = useQuery({
    queryKey: ['system-info'],
    queryFn: configApi.getSystem,
  })

  const { data: healthStatus } = useQuery({
    queryKey: ['health-status'],
    queryFn: healthApi.check,
    refetchInterval: 30000, // Check every 30 seconds
  })

  const { data: playwrightConfig } = useQuery({
    queryKey: ['playwright-config'],
    queryFn: configApi.getPlaywright,
  })

  const tabs = [
    { id: 'system', name: 'System', icon: Server },
    { id: 'playwright', name: 'Playwright', icon: Database },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'appearance', name: 'Appearance', icon: Palette },
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold">Settings</h1>
        <p className="text-muted-foreground">
          Configure your test management system
        </p>
      </div>

      {/* Health Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Server className="mr-2 h-5 w-5" />
            System Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <div className="h-3 w-3 bg-green-500 rounded-full"></div>
              <span className="text-sm">API Server</span>
              <Badge variant="success">Online</Badge>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-3 w-3 bg-green-500 rounded-full"></div>
              <span className="text-sm">WebSocket</span>
              <Badge variant="success">Connected</Badge>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-3 w-3 bg-green-500 rounded-full"></div>
              <span className="text-sm">Test Discovery</span>
              <Badge variant="success">Active</Badge>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-3 w-3 bg-yellow-500 rounded-full"></div>
              <span className="text-sm">Scheduler</span>
              <Badge variant="warning">Idle</Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar */}
        <div className="space-y-2">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === tab.id
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:text-foreground hover:bg-accent'
              }`}
            >
              <tab.icon className="mr-3 h-4 w-4" />
              {tab.name}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="lg:col-span-3 space-y-6">
          {activeTab === 'system' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>System Information</CardTitle>
                  <CardDescription>
                    Current system status and configuration
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {systemInfo && (
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Node Version:</span>
                        <span className="ml-2 font-medium">{systemInfo.nodeVersion}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Platform:</span>
                        <span className="ml-2 font-medium">{systemInfo.platform}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Architecture:</span>
                        <span className="ml-2 font-medium">{systemInfo.arch}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Environment:</span>
                        <span className="ml-2 font-medium">{systemInfo.env}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Uptime:</span>
                        <span className="ml-2 font-medium">
                          {Math.floor(systemInfo.uptime / 3600)}h {Math.floor((systemInfo.uptime % 3600) / 60)}m
                        </span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Memory Usage:</span>
                        <span className="ml-2 font-medium">
                          {Math.round(systemInfo.memory.heapUsed / 1024 / 1024)}MB
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Performance Settings</CardTitle>
                  <CardDescription>
                    Configure system performance parameters
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Max Concurrent Executions
                    </label>
                    <input
                      type="number"
                      defaultValue={2}
                      min={1}
                      max={10}
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Test Discovery Interval (minutes)
                    </label>
                    <input
                      type="number"
                      defaultValue={30}
                      min={5}
                      max={120}
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Log Retention (days)
                    </label>
                    <input
                      type="number"
                      defaultValue={30}
                      min={1}
                      max={365}
                      className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'playwright' && (
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Playwright Configuration</CardTitle>
                  <CardDescription>
                    Current Playwright test configuration
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {playwrightConfig && (
                    <div className="space-y-4">
                      <div>
                        <span className="text-sm text-muted-foreground">Test Directory:</span>
                        <span className="ml-2 font-mono text-sm">{playwrightConfig.testDir}</span>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Workers:</span>
                        <span className="ml-2 font-medium">{playwrightConfig.workers}</span>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Timeout:</span>
                        <span className="ml-2 font-medium">{playwrightConfig.timeout}ms</span>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">Retries:</span>
                        <span className="ml-2 font-medium">{playwrightConfig.retries}</span>
                      </div>
                      
                      <div className="mt-4">
                        <h4 className="font-medium mb-2">Projects:</h4>
                        <div className="space-y-2">
                          {playwrightConfig.projects.map((project, index) => (
                            <div key={index} className="p-3 border rounded-md">
                              <div className="font-medium">{project.name}</div>
                              <div className="text-sm text-muted-foreground">
                                Headless: {project.use.headless ? 'Yes' : 'No'}
                              </div>
                              {project.use.storageState && (
                                <div className="text-sm text-muted-foreground">
                                  Auth: {project.use.storageState}
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'notifications' && (
            <Card>
              <CardHeader>
                <CardTitle>Notification Settings</CardTitle>
                <CardDescription>
                  Configure how you receive test notifications
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Email Notifications</div>
                      <div className="text-sm text-muted-foreground">
                        Receive email alerts for test failures
                      </div>
                    </div>
                    <input type="checkbox" className="rounded" />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Browser Notifications</div>
                      <div className="text-sm text-muted-foreground">
                        Show browser notifications for test completion
                      </div>
                    </div>
                    <input type="checkbox" defaultChecked className="rounded" />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Slack Integration</div>
                      <div className="text-sm text-muted-foreground">
                        Send test results to Slack channels
                      </div>
                    </div>
                    <input type="checkbox" className="rounded" />
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <label className="block text-sm font-medium mb-2">
                    Email Recipients
                  </label>
                  <input
                    type="email"
                    placeholder="Enter email addresses (comma separated)"
                    className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {activeTab === 'security' && (
            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
                <CardDescription>
                  Manage security and access controls
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">API Authentication</div>
                      <div className="text-sm text-muted-foreground">
                        Require authentication for API access
                      </div>
                    </div>
                    <input type="checkbox" defaultChecked className="rounded" />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">CORS Protection</div>
                      <div className="text-sm text-muted-foreground">
                        Enable Cross-Origin Resource Sharing protection
                      </div>
                    </div>
                    <input type="checkbox" defaultChecked className="rounded" />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Rate Limiting</div>
                      <div className="text-sm text-muted-foreground">
                        Limit API requests per minute
                      </div>
                    </div>
                    <input type="checkbox" defaultChecked className="rounded" />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {activeTab === 'appearance' && (
            <Card>
              <CardHeader>
                <CardTitle>Appearance Settings</CardTitle>
                <CardDescription>
                  Customize the look and feel of the interface
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Theme</label>
                  <select className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    <option>Light</option>
                    <option>Dark</option>
                    <option>System</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Accent Color</label>
                  <div className="flex space-x-2">
                    {['blue', 'green', 'purple', 'red', 'orange'].map(color => (
                      <button
                        key={color}
                        className={`w-8 h-8 rounded-full bg-${color}-500 border-2 border-transparent hover:border-gray-300`}
                      />
                    ))}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-2">Font Size</label>
                  <select className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary">
                    <option>Small</option>
                    <option>Medium</option>
                    <option>Large</option>
                  </select>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Save Button */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Reset to Defaults
            </Button>
            <Button>
              <Save className="mr-2 h-4 w-4" />
              Save Changes
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

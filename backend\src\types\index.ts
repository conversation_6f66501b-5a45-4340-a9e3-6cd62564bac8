export interface TestSuite {
  id: string
  name: string
  description?: string
  filePath: string
  tests: Test[]
  project: string
  tags?: string[]
  lastRun?: Date
  status?: 'passed' | 'failed' | 'skipped' | 'running'
}

export interface Test {
  id: string
  name: string
  description?: string
  filePath: string
  line: number
  suite: string
  project: string
  tags?: string[]
  severity?: 'blocker' | 'critical' | 'normal' | 'minor' | 'trivial'
  status?: 'passed' | 'failed' | 'skipped' | 'running'
  duration?: number
  error?: string
  lastRun?: Date
  retries?: number
}

export interface TestExecution {
  id: string
  testId?: string
  suiteId?: string
  project: string
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled'
  startTime: Date
  endTime?: Date
  duration?: number
  output: string[]
  results?: TestResult[]
  command: string
  options?: ExecutionOptions
}

export interface TestResult {
  testId: string
  status: 'passed' | 'failed' | 'skipped'
  duration: number
  error?: string
  retries: number
  attachments?: string[]
}

export interface ExecutionOptions {
  headless?: boolean
  workers?: number
  retries?: number
  timeout?: number
  grep?: string
  project?: string
}

export interface TestPlan {
  id: string
  name: string
  description?: string
  tests: string[]
  suites: string[]
  projects: string[]
  schedule?: {
    enabled: boolean
    cron: string
    timezone: string
  }
  notifications?: {
    email: string[]
    slack?: string
  }
  createdAt: Date
  updatedAt: Date
  lastRun?: Date
  status?: 'active' | 'inactive' | 'running'
}

export interface PlaywrightProject {
  name: string
  testMatch: string[]
  testIgnore?: string[]
  use: {
    headless?: boolean
    storageState?: string
    [key: string]: any
  }
}

export interface PlaywrightConfig {
  testDir: string
  projects: PlaywrightProject[]
  reporter: string[][]
  timeout: number
  workers: number
  retries: number
}

export interface SystemStats {
  totalTests: number
  totalSuites: number
  passRate: number
  avgDuration: number
  lastRunTime: Date
  activeExecutions: number
  queuedExecutions: number
}

export interface AllureReport {
  uuid: string
  name: string
  status: string
  time: {
    start: number
    stop: number
    duration: number
  }
  attachments: Array<{
    name: string
    source: string
    type: string
  }>
}

export interface LogEntry {
  timestamp: Date
  level: 'info' | 'warn' | 'error' | 'debug'
  message: string
  executionId?: string
  testId?: string
  metadata?: Record<string, any>
}

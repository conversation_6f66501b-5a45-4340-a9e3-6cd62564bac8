import { Router } from 'express'
import { logger } from '../utils/logger.js'
import type { TestExecutionService } from '../services/testExecution.js'

export const executionRoutes = Router()

// Start test execution
executionRoutes.post('/start', async (req, res) => {
  try {
    const testExecution: TestExecutionService = req.app.locals.testExecution
    const { tests, suites, projects, command, options } = req.body
    
    const execution = await testExecution.startExecution({
      tests,
      suites,
      projects,
      command,
      options
    })
    
    res.json(execution)
  } catch (error) {
    logger.error('Failed to start execution:', error)
    res.status(500).json({
      error: 'Failed to start test execution',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get all executions
executionRoutes.get('/', async (req, res) => {
  try {
    const testExecution: TestExecutionService = req.app.locals.testExecution
    const { status, limit = 50, offset = 0 } = req.query
    
    const executions = await testExecution.getExecutions({
      status: status as string,
      limit: parseInt(limit as string),
      offset: parseInt(offset as string)
    })
    
    res.json(executions)
  } catch (error) {
    logger.error('Failed to get executions:', error)
    res.status(500).json({
      error: 'Failed to retrieve executions',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get specific execution
executionRoutes.get('/:id', async (req, res) => {
  try {
    const testExecution: TestExecutionService = req.app.locals.testExecution
    const execution = await testExecution.getExecution(req.params.id)
    
    if (!execution) {
      return res.status(404).json({ error: 'Execution not found' })
    }
    
    res.json(execution)
  } catch (error) {
    logger.error('Failed to get execution:', error)
    res.status(500).json({
      error: 'Failed to retrieve execution',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Cancel execution
executionRoutes.post('/:id/cancel', async (req, res) => {
  try {
    const testExecution: TestExecutionService = req.app.locals.testExecution
    await testExecution.cancelExecution(req.params.id)
    res.json({ message: 'Execution cancelled successfully' })
  } catch (error) {
    logger.error('Failed to cancel execution:', error)
    res.status(500).json({
      error: 'Failed to cancel execution',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get execution logs
executionRoutes.get('/:id/logs', async (req, res) => {
  try {
    const testExecution: TestExecutionService = req.app.locals.testExecution
    const { since } = req.query
    
    const logs = await testExecution.getExecutionLogs(req.params.id, {
      since: since ? new Date(since as string) : undefined
    })
    
    res.json(logs)
  } catch (error) {
    logger.error('Failed to get execution logs:', error)
    res.status(500).json({
      error: 'Failed to retrieve execution logs',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get active executions
executionRoutes.get('/active', async (req, res) => {
  try {
    const testExecution: TestExecutionService = req.app.locals.testExecution
    const activeExecutions = testExecution.getActiveExecutions()
    res.json(activeExecutions)
  } catch (error) {
    logger.error('Failed to get active executions:', error)
    res.status(500).json({
      error: 'Failed to retrieve active executions',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get execution queue
executionRoutes.get('/queue', async (req, res) => {
  try {
    const testExecution: TestExecutionService = req.app.locals.testExecution
    const queuedExecutions = testExecution.getQueuedExecutions()
    res.json(queuedExecutions)
  } catch (error) {
    logger.error('Failed to get execution queue:', error)
    res.status(500).json({
      error: 'Failed to retrieve execution queue',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

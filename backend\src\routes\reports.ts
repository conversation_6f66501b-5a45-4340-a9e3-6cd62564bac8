import { Router } from 'express'
import { logger } from '../utils/logger.js'
import path from 'path'
import fs from 'fs/promises'

export const reportsRoutes = Router()

// Get Allure report data
reportsRoutes.get('/allure', async (req, res) => {
  try {
    const allureResultsPath = path.join(process.cwd(), '..', 'allure-results')
    const files = await fs.readdir(allureResultsPath)
    
    const results = []
    for (const file of files) {
      if (file.endsWith('-result.json')) {
        const filePath = path.join(allureResultsPath, file)
        const content = await fs.readFile(filePath, 'utf-8')
        results.push(JSON.parse(content))
      }
    }
    
    res.json(results)
  } catch (error) {
    logger.error('Failed to get Allure reports:', error)
    res.status(500).json({
      error: 'Failed to retrieve Allure reports',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get Playwright HTML report
reportsRoutes.get('/playwright', async (req, res) => {
  try {
    const reportPath = path.join(process.cwd(), '..', 'playwright-report', 'index.html')
    const reportExists = await fs.access(reportPath).then(() => true).catch(() => false)
    
    if (!reportExists) {
      return res.status(404).json({ error: 'Playwright report not found' })
    }
    
    const reportContent = await fs.readFile(reportPath, 'utf-8')
    res.setHeader('Content-Type', 'text/html')
    res.send(reportContent)
  } catch (error) {
    logger.error('Failed to get Playwright report:', error)
    res.status(500).json({
      error: 'Failed to retrieve Playwright report',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get test execution logs
reportsRoutes.get('/logs', async (req, res) => {
  try {
    const logsPath = path.join(process.cwd(), '..', 'logs')
    const files = await fs.readdir(logsPath)
    
    const logFiles = files
      .filter(file => file.startsWith('test-execution-') && file.endsWith('.log'))
      .sort((a, b) => b.localeCompare(a)) // Most recent first
    
    const logs = []
    for (const file of logFiles.slice(0, 10)) { // Last 10 log files
      const filePath = path.join(logsPath, file)
      const stats = await fs.stat(filePath)
      logs.push({
        filename: file,
        size: stats.size,
        modified: stats.mtime,
        path: `/api/reports/logs/${file}`
      })
    }
    
    res.json(logs)
  } catch (error) {
    logger.error('Failed to get execution logs:', error)
    res.status(500).json({
      error: 'Failed to retrieve execution logs',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get specific log file
reportsRoutes.get('/logs/:filename', async (req, res) => {
  try {
    const { filename } = req.params
    
    // Security check - only allow log files
    if (!filename.startsWith('test-execution-') || !filename.endsWith('.log')) {
      return res.status(400).json({ error: 'Invalid log file name' })
    }
    
    const logPath = path.join(process.cwd(), '..', 'logs', filename)
    const logExists = await fs.access(logPath).then(() => true).catch(() => false)
    
    if (!logExists) {
      return res.status(404).json({ error: 'Log file not found' })
    }
    
    const logContent = await fs.readFile(logPath, 'utf-8')
    res.setHeader('Content-Type', 'text/plain')
    res.send(logContent)
  } catch (error) {
    logger.error('Failed to get log file:', error)
    res.status(500).json({
      error: 'Failed to retrieve log file',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get test coverage data (if available)
reportsRoutes.get('/coverage', async (req, res) => {
  try {
    const coveragePath = path.join(process.cwd(), '..', 'coverage', 'coverage-summary.json')
    const coverageExists = await fs.access(coveragePath).then(() => true).catch(() => false)
    
    if (!coverageExists) {
      return res.status(404).json({ error: 'Coverage report not found' })
    }
    
    const coverageContent = await fs.readFile(coveragePath, 'utf-8')
    res.json(JSON.parse(coverageContent))
  } catch (error) {
    logger.error('Failed to get coverage report:', error)
    res.status(500).json({
      error: 'Failed to retrieve coverage report',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

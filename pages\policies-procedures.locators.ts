/**
 * Policies Procedures module locators
 * Contains all selectors and locators for the Policies Procedures page
 */

export const policiesProceduresLocators = {
  // Navigation
  navigation: {
    policiesAndProceduresLink: 'link[name="Policies And Procedures"]'
  },

  // Table and pagination
  table: {
    policyRows: 'tr',
    nameCell: 'td',
    downloadButton: 'button.btn.btn-link'
  },

  // Pagination controls
  pagination: {
    itemsPerPageButton: 'button[name="20"]',
    fiftyItemsOption: 'text=50'
  },

  // Modal and confirmation
  modal: {
    swalModal: '.swal-modal',
    swalTitle: '.swal-title',
    confirmButton: 'button[name="Confirm"]'
  }
};

/**
 * Policies Procedures page role-based locators for better accessibility
 */
export const policiesProceduresRoleLocators = {
  policiesAndProceduresLink: { role: 'link', name: 'Policies And Procedures' },
  itemsPerPageButton: { role: 'button', name: '20' },
  fiftyItemsText: { text: '50', exact: true },
  confirmButton: { role: 'button', name: 'Confirm' }
};

import { useState, useEffect, useRef } from 'react'
import { useQuery } from '@tanstack/react-query'
import { 
  Play, 
  Square, 
  RefreshCw, 
  Terminal, 
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Download
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { executionApi, configApi } from '@/services/api'
import { useSocketStore } from '@/stores/socketStore'
import { cn, formatDuration, formatDate, getStatusColor } from '@/lib/utils'
import type { TestExecution } from '@/types'

export function TestExecution() {
  const [selectedExecution, setSelectedExecution] = useState<TestExecution | null>(null)
  const [showNewExecutionForm, setShowNewExecutionForm] = useState(false)
  const [executionParams, setExecutionParams] = useState({
    projects: [] as string[],
    command: '',
    options: {
      headless: true,
      workers: 2,
      retries: 0,
    }
  })
  
  const logsEndRef = useRef<HTMLDivElement>(null)
  const { 
    startExecution, 
    cancelExecution, 
    joinExecution, 
    leaveExecution,
    executionLogs,
    activeExecutions 
  } = useSocketStore()

  const { data: executions = [], refetch: refetchExecutions } = useQuery({
    queryKey: ['executions'],
    queryFn: () => executionApi.getAll({ limit: 20 }),
    refetchInterval: 5000,
  })

  const { data: projects = [] } = useQuery({
    queryKey: ['projects'],
    queryFn: configApi.getProjects,
  })

  useEffect(() => {
    if (selectedExecution) {
      joinExecution(selectedExecution.id)
      return () => leaveExecution(selectedExecution.id)
    }
  }, [selectedExecution, joinExecution, leaveExecution])

  useEffect(() => {
    // Auto-scroll logs to bottom
    if (logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }, [executionLogs, selectedExecution])

  const handleStartExecution = () => {
    startExecution(executionParams)
    setShowNewExecutionForm(false)
    setExecutionParams({
      projects: [],
      command: '',
      options: { headless: true, workers: 2, retries: 0 }
    })
  }

  const handleCancelExecution = (executionId: string) => {
    cancelExecution(executionId)
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'running':
        return <Play className="h-4 w-4 text-blue-600 animate-pulse" />
      case 'queued':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'cancelled':
        return <Square className="h-4 w-4 text-gray-600" />
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />
    }
  }

  const currentLogs = selectedExecution ? executionLogs.get(selectedExecution.id) || [] : []

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Test Execution</h1>
          <p className="text-muted-foreground">
            Monitor and control test execution
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            onClick={() => refetchExecutions()}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button onClick={() => setShowNewExecutionForm(true)}>
            <Play className="mr-2 h-4 w-4" />
            New Execution
          </Button>
        </div>
      </div>

      {/* New Execution Form */}
      {showNewExecutionForm && (
        <Card>
          <CardHeader>
            <CardTitle>Start New Execution</CardTitle>
            <CardDescription>
              Configure and start a new test execution
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Projects</label>
              <div className="grid grid-cols-2 gap-2">
                {projects.map((project: any) => (
                  <label key={project.name} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={executionParams.projects.includes(project.name)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setExecutionParams(prev => ({
                            ...prev,
                            projects: [...prev.projects, project.name]
                          }))
                        } else {
                          setExecutionParams(prev => ({
                            ...prev,
                            projects: prev.projects.filter(p => p !== project.name)
                          }))
                        }
                      }}
                      className="rounded"
                    />
                    <span className="text-sm">{project.name}</span>
                  </label>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Custom Command (Optional)</label>
              <input
                type="text"
                value={executionParams.command}
                onChange={(e) => setExecutionParams(prev => ({ ...prev, command: e.target.value }))}
                placeholder="npx playwright test --project=approved-tests"
                className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-background"
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Workers</label>
                <input
                  type="number"
                  value={executionParams.options.workers}
                  onChange={(e) => setExecutionParams(prev => ({
                    ...prev,
                    options: { ...prev.options, workers: parseInt(e.target.value) }
                  }))}
                  min="1"
                  max="10"
                  className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-background"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Retries</label>
                <input
                  type="number"
                  value={executionParams.options.retries}
                  onChange={(e) => setExecutionParams(prev => ({
                    ...prev,
                    options: { ...prev.options, retries: parseInt(e.target.value) }
                  }))}
                  min="0"
                  max="5"
                  className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary bg-background"
                />
              </div>
              <div className="flex items-center space-x-2 pt-6">
                <input
                  type="checkbox"
                  id="headless"
                  checked={executionParams.options.headless}
                  onChange={(e) => setExecutionParams(prev => ({
                    ...prev,
                    options: { ...prev.options, headless: e.target.checked }
                  }))}
                  className="rounded"
                />
                <label htmlFor="headless" className="text-sm">Headless</label>
              </div>
            </div>

            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowNewExecutionForm(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleStartExecution}
                disabled={executionParams.projects.length === 0 && !executionParams.command}
              >
                Start Execution
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Executions List */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Executions</h2>
          
          {/* Active Executions */}
          {activeExecutions.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">Active</h3>
              {activeExecutions.map((execution) => (
                <Card
                  key={execution.id}
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    selectedExecution?.id === execution.id && "ring-2 ring-primary"
                  )}
                  onClick={() => setSelectedExecution(execution)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(execution.status)}
                          <span className="font-medium">
                            {execution.id.slice(0, 8)}...
                          </span>
                          <Badge variant="outline" className="animate-pulse">
                            {execution.status}
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">
                          {execution.project} • {formatDate(execution.startTime)}
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleCancelExecution(execution.id)
                        }}
                      >
                        <Square className="h-3 w-3 mr-1" />
                        Cancel
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Recent Executions */}
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">Recent</h3>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {executions
                .filter(exec => !activeExecutions.find(active => active.id === exec.id))
                .map((execution) => (
                <Card
                  key={execution.id}
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    selectedExecution?.id === execution.id && "ring-2 ring-primary"
                  )}
                  onClick={() => setSelectedExecution(execution)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(execution.status)}
                          <span className="font-medium">
                            {execution.id.slice(0, 8)}...
                          </span>
                          <Badge className={getStatusColor(execution.status)}>
                            {execution.status}
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground mt-1">
                          {execution.project} • {formatDate(execution.startTime)}
                          {execution.duration && (
                            <span> • {formatDuration(execution.duration)}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>

        {/* Execution Details & Logs */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">
              {selectedExecution ? 'Execution Details' : 'Select an execution'}
            </h2>
            {selectedExecution && (
              <Button size="sm" variant="outline">
                <Download className="h-4 w-4 mr-1" />
                Export Logs
              </Button>
            )}
          </div>

          {selectedExecution ? (
            <div className="space-y-4">
              {/* Execution Info */}
              <Card>
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">
                      {selectedExecution.id.slice(0, 8)}...
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(selectedExecution.status)}
                      <Badge className={getStatusColor(selectedExecution.status)}>
                        {selectedExecution.status}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-0 space-y-2">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Project:</span>
                      <span className="ml-2 font-medium">{selectedExecution.project}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Started:</span>
                      <span className="ml-2 font-medium">
                        {formatDate(selectedExecution.startTime)}
                      </span>
                    </div>
                    {selectedExecution.duration && (
                      <div>
                        <span className="text-muted-foreground">Duration:</span>
                        <span className="ml-2 font-medium">
                          {formatDuration(selectedExecution.duration)}
                        </span>
                      </div>
                    )}
                    <div>
                      <span className="text-muted-foreground">Command:</span>
                      <span className="ml-2 font-mono text-xs">
                        {selectedExecution.command}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Live Logs */}
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center">
                    <Terminal className="mr-2 h-4 w-4" />
                    Live Logs
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="bg-black text-green-400 p-4 rounded-md font-mono text-sm h-96 overflow-y-auto">
                    {currentLogs.length === 0 ? (
                      <div className="text-gray-500">No logs available...</div>
                    ) : (
                      <div className="space-y-1">
                        {currentLogs.map((log, index) => (
                          <div key={index} className="flex">
                            <span className="text-gray-500 mr-2">
                              {log.timestamp.toLocaleTimeString()}
                            </span>
                            <span className={cn(
                              log.level === 'error' && 'text-red-400',
                              log.level === 'warn' && 'text-yellow-400',
                              log.level === 'info' && 'text-green-400'
                            )}>
                              {log.message}
                            </span>
                          </div>
                        ))}
                        <div ref={logsEndRef} />
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <Terminal className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Select an execution to view details and logs
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

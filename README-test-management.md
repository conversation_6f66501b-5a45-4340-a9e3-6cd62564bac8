# INCONNECT Test Management System

A modern React + TypeScript frontend with Node.js backend for managing Playwright test automation.

## Features

- 🎯 **Test Suite Display**: Browse and search test suites with modern UI
- ⚡ **Real-time Execution**: Live test execution with streaming logs
- 📊 **Test Plans**: Organize and schedule test executions
- 📈 **Reports & Analytics**: Allure reports, Playwright reports, and execution logs
- 🔧 **Settings**: Configure system, notifications, and appearance
- 🌐 **WebSocket Integration**: Real-time updates and notifications

## Architecture

```
INCONNECT - Playwright Testing/
├── frontend/                 # React + TypeScript frontend
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   ├── pages/          # Main application pages
│   │   ├── services/       # API communication
│   │   ├── stores/         # Zustand state management
│   │   └── types/          # TypeScript definitions
│   └── package.json
├── backend/                 # Node.js + Express backend
│   ├── src/
│   │   ├── routes/         # API endpoints
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utilities and helpers
│   └── package.json
└── [existing Playwright files...]
```

## Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **Shadcn/ui** for modern UI components
- **Tanstack Query** for data fetching
- **Zustand** for state management
- **Socket.io Client** for real-time updates
- **Framer Motion** for animations

### Backend
- **Node.js** with TypeScript
- **Express.js** web framework
- **Socket.io** for real-time communication
- **Winston** for logging
- **TypeScript AST parsing** for test discovery
- **Child processes** for test execution

## Quick Start

### 1. Install Dependencies

```bash
# Install frontend dependencies
cd frontend
npm install

# Install backend dependencies
cd ../backend
npm install
```

### 2. Environment Setup

```bash
# Backend environment
cd backend
cp .env.example .env
# Edit .env with your configuration

# Create logs directory
mkdir -p logs
```

### 3. Start Development Servers

```bash
# Terminal 1: Start backend
cd backend
npm run dev

# Terminal 2: Start frontend
cd frontend
npm run dev
```

### 4. Access the Application

- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- Health Check: http://localhost:3001/api/health

## API Endpoints

### Tests
- `GET /api/tests/suites` - Get all test suites
- `GET /api/tests` - Get all tests with filters
- `GET /api/tests/search` - Search tests
- `POST /api/tests/refresh` - Refresh test discovery

### Execution
- `POST /api/execution/start` - Start test execution
- `GET /api/execution` - Get execution history
- `GET /api/execution/:id` - Get specific execution
- `POST /api/execution/:id/cancel` - Cancel execution

### Reports
- `GET /api/reports/allure` - Get Allure reports
- `GET /api/reports/playwright` - Get Playwright HTML report
- `GET /api/reports/logs` - Get execution logs

### Configuration
- `GET /api/config/playwright` - Get Playwright configuration
- `GET /api/config/projects` - Get available projects
- `GET /api/config/system` - Get system information

## WebSocket Events

### Client → Server
- `join-execution` - Join execution room for updates
- `join-dashboard` - Join dashboard for system updates
- `start-test-execution` - Start new test execution
- `cancel-execution` - Cancel running execution

### Server → Client
- `execution-queued` - Execution added to queue
- `execution-started` - Execution started
- `execution-completed` - Execution finished successfully
- `execution-failed` - Execution failed
- `execution-log` - Real-time log entry
- `system-stats` - System statistics update

## Development

### Frontend Development

```bash
cd frontend

# Start development server
npm run dev

# Build for production
npm run build

# Type checking
npm run type-check

# Linting
npm run lint
```

### Backend Development

```bash
cd backend

# Start development server with hot reload
npm run dev

# Build TypeScript
npm run build

# Start production server
npm start

# Type checking
npm run type-check

# Linting
npm run lint
```

## Deployment

### Frontend (Netlify/Vercel)

1. Build the frontend:
```bash
cd frontend
npm run build
```

2. Deploy the `dist` folder to your hosting provider

### Backend (Node.js hosting)

1. Build the backend:
```bash
cd backend
npm run build
```

2. Deploy with environment variables configured

### Environment Variables

**Backend (.env):**
```
PORT=3001
NODE_ENV=production
FRONTEND_URL=https://your-frontend-domain.com
LOG_LEVEL=info
```

## Integration with Existing Playwright Project

The system automatically discovers and integrates with your existing:
- Test files in `tests/` directory
- Playwright configuration
- Allure reports in `allure-results/`
- Execution logs in `logs/`
- HTML reports in `playwright-report/`

## Features in Detail

### Test Suite Display
- Hierarchical view of test suites and individual tests
- Search and filtering capabilities
- Project-based organization
- Test metadata display (tags, severity, status)

### Real-time Execution
- Queue management for concurrent executions
- Live log streaming via WebSocket
- Execution controls (start, cancel)
- Progress tracking and status updates

### Test Plans
- Create reusable test execution plans
- Schedule automated runs
- Group tests by project or criteria
- Execution history tracking

### Reports & Analytics
- Embedded Allure report viewer
- Playwright HTML report integration
- Execution log browser
- Test metrics and trends

### Settings & Configuration
- System health monitoring
- Playwright configuration display
- Notification preferences
- Appearance customization

## Troubleshooting

### Common Issues

1. **Backend not starting**: Check if port 3001 is available
2. **Frontend not connecting**: Verify backend is running and CORS is configured
3. **Test discovery failing**: Ensure Playwright config is valid and test files exist
4. **WebSocket connection issues**: Check firewall settings and proxy configuration

### Logs

- Backend logs: `backend/logs/`
- Test execution logs: `logs/` (in project root)
- Browser console for frontend issues

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is part of the INCONNECT test automation system.

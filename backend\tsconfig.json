{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "Node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "outDir": "./dist", "rootDir": "./src", "resolveJsonModule": true, "allowImportingTsExtensions": false, "noEmit": false, "isolatedModules": true, "verbatimModuleSyntax": false, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}
import {Page, Locator} from "@playwright/test";
import * as allure from "allure-js-commons";

export class DateFilter {
    private fromDate: Date;
    private toDate: Date;

    constructor(fromDate?: Date | string, toDate?: Date | string) {
        this.toDate = toDate ? new Date(toDate) : new Date();
        this.fromDate = fromDate ? new Date(fromDate) : new Date(this.toDate.getTime() - (30 * 24 * 60 * 60 * 1000));
    }

    getFromDateString(): string {
        return this.fromDate.toISOString().split('T')[0];
    }

    getToDateString(): string {
        return this.toDate.toISOString().split('T')[0];
    }
}

export class Classroom_management_objects {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    /**
     * Navigates to the Attendance section in Classroom Management.
     * @returns {Promise<void>}
     */
    async attendanceLocator(): Promise<void> {
        await this.page.click('text=Classroom Management'); // Navigate to Classroom Management
        await this.page.click('text=Attendance'); // Click on Attendance
    }

    /**
     * Navigates to the Student Success Overview section in Classroom Management.
     * @returns {Promise<void>}
     */
    async studentSuccessLocator(): Promise<void> {
        await this.page.click('text=Classroom Management'); // Navigate to Classroom Management
        await this.page.click('text=Student Success Overview'); // Click on Student Success Overview
    }

    /**
     * Navigates to the Student Success Detail section in Classroom Management.
     * @returns {Promise<void>}
     */
    async studentSuccessDetailLocator(): Promise<void> {
        await this.page.click('text=Classroom Management'); // Navigate to Classroom Management
        await this.page.locator('text=Student Success Detail').click(); // Click on Student Success Detail
        await this.page.click('text=Apply filters');
    }

    /**
     * Navigates to the Timetables section in Classroom Management.
     * @returns {Promise<void>}
     */
    async timetablesLocator(): Promise<void> {
        await this.page.getByText('Classroom Management').click();
        await this.page.getByRole('link', { name: 'Timetables' }).click();
    }

    /**
     * Navigates to the Late/Resubmission Report section in Classroom Management.
     * @returns {Promise<void>}
     */
    async lateResubmissionsLocator(): Promise<void> {
        await this.page.getByText('Classroom Management').click();
        await this.page.getByRole('link', { name: 'Late/Resubmission Report' }).click();
    }

    /**
     * Selects a random option from a dropdown menu.
     * @param dropdown - The dropdown element to select an option from.
     * @param label - The label for the dropdown, used for logging.
     * @returns Promise<void>
     */
    async selectRandomOption(
        dropdown: Locator,
        label: string
    ): Promise<void> {
        try {
            const options = await dropdown.locator('option').all();
            const validOptions = [];
            
            for (const option of options) {
                const value = await option.getAttribute('value');
                if (value && value !== "" && value !== "0") {
                    validOptions.push(option);
                }
            }

            if (validOptions.length > 0) {
                const randomIndex = Math.floor(Math.random() * validOptions.length);
                const option = validOptions[randomIndex];
                const optionValue = await option.getAttribute('value');
                const optionLabel = await option.textContent();

                if (optionValue) {
                    await dropdown.selectOption({ value: optionValue });
                    console.log(`Selected ${label}: ${optionLabel?.trim() ?? ''} (value: ${optionValue})`);
                }
            } else {
                throw new Error(`No valid options available for ${label}`);
            }
        } catch (error) {
            console.error(`Error selecting random option for ${label}:`, error);
            throw error;
        }
    }
}

export class Attendance_Overview_objects {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    /**
     * Applies a random attendance filter based on the number of days and verifies if the attendance data grid is displayed.
     * @returns Promise<void>
     */
    async attendancePresentFilter(): Promise<void> {
        try {
            // Map display text to actual option values
            const dayOptionsMap = {
                '10 days': '0',
                '30 days': '1',
                '60 days': '2', 
                '90 days': '3'
            };
            
            const selectOptions = ['30 days', '60 days', '90 days'] as const;
            const randomSelection = Math.floor(Math.random() * selectOptions.length);
            const optionToSelect = selectOptions[randomSelection];
            const optionValue = dayOptionsMap[optionToSelect];
            
            await this.page.selectOption('#c6', { value: optionValue }); // Present filter dropdown
            console.log(`Selected ${optionToSelect} (value: ${optionValue})`);
            
            await this.page.click('#c7'); // "Apply" button
            await this.page.waitForTimeout(3000);

            const manageCampusButtons = await this.page.$$('.btn-success');
            if (manageCampusButtons.length === 0) {
                throw new Error('No manage campus buttons found');
            }
            
            const randomButton = Math.floor(Math.random() * manageCampusButtons.length);
            await manageCampusButtons[randomButton].click();
            await this.page.waitForTimeout(3000);
            await this.page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });

            await this.page.waitForTimeout(3000);
            const dataGrid = this.page.locator('#ComplianceGrid table tbody');
            const isVisible = await dataGrid.isVisible();
            
            if (isVisible) {
                console.log("Present filter | Attendance data grid is displayed");
            } else {
                throw new Error("Present filter | Attendance data grid is not displayed");
            }
            
            await this.page.waitForTimeout(3000);
        } catch (error) {
            console.error('Error in attendancePresentFilter:', error);
            throw error;
        }
    }

    /**
     * Applies a date range filter for attendance and verifies if the attendance data grid is displayed.
     * @param fromDate - Optional start date (defaults to 30 days ago if not provided)
     * @param toDate - Optional end date (defaults to current date if not provided)
     * @returns Promise<void>
     */
    async attendanceDateFilter(fromDate?: Date | string, toDate?: Date | string): Promise<void> {
        try {
            const dateFilter = new DateFilter(fromDate, toDate);

            await this.page.waitForTimeout(3000);
            await this.page.fill('#c8', dateFilter.getFromDateString());
            const fromDateScreenshot = await this.page.screenshot();
            await allure.attachment("FromDate", fromDateScreenshot, "image/png");

            await this.page.waitForTimeout(3000);
            await this.page.fill('#c9', dateFilter.getToDateString());
            const toDateScreenshot = await this.page.screenshot();
            await allure.attachment("ToDate", toDateScreenshot, "image/png");

            await this.page.click('#c11');
            await this.page.waitForTimeout(3000);

            const manageCampusButtons = await this.page.$$('.btn-success');
            if (manageCampusButtons.length === 0) {
                throw new Error('No manage campus buttons found');
            }
            
            const randomButton = Math.floor(Math.random() * manageCampusButtons.length);
            await manageCampusButtons[randomButton].click();
            await this.page.waitForTimeout(3000);
            await this.page.evaluate(() => {
                window.scrollTo(0, document.body.scrollHeight);
            });
            
            const dataGridAfterFilter = this.page.locator('#ComplianceGrid table tbody');
            const isVisible = await dataGridAfterFilter.isVisible();
            
            await this.page.waitForTimeout(3000);
            const gridScreenshot = await this.page.screenshot();
            await allure.attachment("AttendanceDataGrid", gridScreenshot, "image/png");

            if (isVisible) {
                console.log("From & To date | Attendance data grid is displayed");
            } else {
                throw new Error("From & To date | Attendance data grid is not displayed");
            }
        } catch (error) {
            console.error('Error in attendanceDateFilter:', error);
            throw error;
        }
    }

    /**
     * Selects a random campus from the dropdown and logs the selected campus.
     * @returns Promise<void>
     */
    async attendanceDetailCampus(): Promise<void> {
        try {
            const dropdownArrow = await this.page.$('.customselect-dropdown > span');
            if (!dropdownArrow) {
                throw new Error('Dropdown arrow not found');
            }
            
            await dropdownArrow.click();
            
            const labels = await this.page.$$eval(
                '.d-flex.single-mode > input[name="c31"] + label',
                (elements: HTMLLabelElement[]) => elements
                    .map(el => el.textContent?.trim() ?? '')
                    .filter(text => text !== '--Please Select--')
            );

            if (labels.length === 0) {
                throw new Error('No campus labels found');
            }

            const randomIndex = Math.floor(Math.random() * labels.length);
            const randomLabel = labels[randomIndex];
            
            await this.page.click(
                `.d-flex.single-mode > input[name="c31"] + label:has-text("${randomLabel}")`,
                {force: true}
            );
            
            console.log(`Selected Campus: ${randomLabel}`);
        } catch (error) {
            console.error('Error in attendanceDetailCampus:', error);
            throw error;
        }
    }
}

export class Student_Success_Overview_objects {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    /**
     * Selects a random qualification from a dropdown menu, avoiding specific values.
     * @returns Promise<void>
     */
    async randomQualification(): Promise<void> {
        await this.page.waitForTimeout(3000);
        try {
            const avoidValues = ['0', '1', '4', '5', '14', '15', '16', '17', '18', '19', '28', '29'];
            const dropdown = this.page.locator('#c5');
            const options = await dropdown.locator('option').all();
            const validOptions = [];

            for (const option of options) {
                const value = await option.getAttribute('value');
                if (value && !avoidValues.includes(value)) {
                    validOptions.push(option);
                }
            }

            if (validOptions.length === 0) {
                throw new Error('No valid qualification options found');
            }

            const randomIndex = Math.floor(Math.random() * validOptions.length);
            const selectedOption = validOptions[randomIndex];
            const value = await selectedOption.getAttribute('value');

            if (!value) {
                throw new Error('Selected option has no value');
            }

            await dropdown.selectOption({ value });
            console.log(`Selected Qualification: ${value}`);
        } catch (error) {
            console.error('Error in randomQualification:', error);
            throw error;
        }
    }

    async randomQualificationStudentSuccessDetail(): Promise<void> {
        await this.page.waitForTimeout(3000);
        await this.page.locator('#collapseFilters').click();
        try {
            const avoidValues = ['0', '1', '4', '5', '14', '15', '16', '17', '18', '19', '28', '29', '30'];
            const dropdown = this.page.locator('#c1');
            const options = await dropdown.locator('option').all();
            const validOptions = [];

            for (const option of options) {
                const value = await option.getAttribute('value');
                if (value && !avoidValues.includes(value)) {
                    validOptions.push(option);
                }
            }

            if (validOptions.length === 0) {
                throw new Error('No valid qualification options found');
            }

            const randomIndex = Math.floor(Math.random() * validOptions.length);
            const selectedOption = validOptions[randomIndex];
            const value = await selectedOption.getAttribute('value');

            if (!value) {
                throw new Error('Selected option has no value');
            }

            await dropdown.selectOption({ value });
            console.log(`Selected Qualification: ${value}`);
        } catch (error) {
            console.error('Error in randomQualification:', error);
            throw error;
        }
    }
}

export class Timetable_objects {
    private page: Page;

    constructor(page: Page) {
        this.page = page;
    }

    /**
     * Selects a random campus from a dropdown menu, avoiding specific values.
     * @returns Promise<void>
     */
    async randomCampus(): Promise<void> {
        try {
            const avoidValues = ['0', '2'];
            const dropdown = this.page.locator('#c1');
            const options = await dropdown.locator('option').all();
            const validOptions = [];

            for (const option of options) {
                const value = await option.getAttribute('value');
                if (value && !avoidValues.includes(value)) {
                    validOptions.push(option);
                }
            }

            if (validOptions.length === 0) {
                throw new Error('No valid campus options found');
            }

            const randomIndex = Math.floor(Math.random() * validOptions.length);
            const selectedOption = validOptions[randomIndex];
            const value = await selectedOption.getAttribute('value');

            if (!value) {
                throw new Error('Selected option has no value');
            }

            await dropdown.selectOption({ value });
            console.log(`Selected Campus: ${value}`);
        } catch (error) {
            console.error('Error in randomCampus:', error);
            throw error;
        }
    }

    /**
     * Finds a session by repeatedly checking for a module code and clicking on it.
     * @returns Promise<void>
     */
    async findSession(): Promise<void> {
        try {
            let moduleFound = false;
            let attempts = 0;
            const maxAttempts = 10;

            while (!moduleFound && attempts < maxAttempts) {
                const moduleCodeElement = await this.page.$('span.ModuleCode');
                if (moduleCodeElement) {
                    const moduleCode = await moduleCodeElement.textContent();
                    console.log(`Module Code: ${moduleCode}`);
                    await this.page.waitForTimeout(3000);
                    await moduleCodeElement.click();
                    moduleFound = true;
                } else {
                    await this.page.click('button[onclick*="TimetableManagementForm"]', { force: true });
                    await this.page.waitForTimeout(3000);
                    attempts++;
                }
            }

            if (!moduleFound) {
                throw new Error('Failed to find module after maximum attempts');
            }
        } catch (error) {
            console.error('Error in findSession:', error);
            throw error;
        }
    }

    /**
     * Clicks on an empty session block to create a new session.
     * @returns Promise<void>
     */
    async blankSession(): Promise<void> {
        try {
            const emptySessionBlock = await this.page.$('.EmptySessionBlock');
            if (!emptySessionBlock) {
                throw new Error('No empty session block found');
            }

            await emptySessionBlock.hover();
            await this.page.waitForTimeout(1000);
            await emptySessionBlock.click();
        } catch (error) {
            console.error('Error in blankSession:', error);
            throw error;
        }
    }
}

import { LateResubData, readLateResubCSV } from './csv-utils';

export class LateResub_objects {
    private page: Page;
    private lateResubData: LateResubData[] | null = null;
    private currentCombination: LateResubData | null = null;

    constructor(page: Page) {
        this.page = page;
    }

    /**
     * Applies a date range filter for late resubmissions and attaches screenshots of the date inputs.
     * @param fromDate - Optional start date (defaults to 30 days ago if not provided)
     * @param toDate - Optional end date (defaults to current date if not provided)
     * @returns Promise<void>
     */
    async dateFilter(fromDate?: Date | string, toDate?: Date | string): Promise<void> {
        try {
            const dateFilter = new DateFilter(fromDate, toDate);

            await this.page.waitForTimeout(3000);
            await this.page.fill('#c2', dateFilter.getFromDateString());
            const fromDateScreenshot = await this.page.screenshot();
            await allure.attachment("FromDate", fromDateScreenshot, "image/png");

            await this.page.waitForTimeout(3000);
            await this.page.fill('#c3', dateFilter.getToDateString());
            const toDateScreenshot = await this.page.screenshot();
            await allure.attachment("ToDate", toDateScreenshot, "image/png");
            
            await this.page.waitForTimeout(2000);
        } catch (error) {
            console.error('Error in dateFilter:', error);
            throw error;
        }
    }

    /**
     * Loads the CSV data if not already loaded
     */
    private async loadCSVData(): Promise<void> {
        if (!this.lateResubData) {
            this.lateResubData = await readLateResubCSV('data/late-resub-data.csv');
        }
    }

    /**
     * Gets a random combination from the CSV data
     */
    private getCombination(): LateResubData {
        if (!this.lateResubData || this.lateResubData.length === 0) {
            throw new Error('No data loaded from CSV file');
        }
        if (!this.currentCombination) {
            const randomIndex = Math.floor(Math.random() * this.lateResubData.length);
            this.currentCombination = this.lateResubData[randomIndex];
        }
        return this.currentCombination;
    }

    private qualificationMapping: { [key: string]: string } = {
        'Bachelor  of  Design (110828)': '10',  // Fixed: was '6', now '10'
        'Advanced Diploma in User Experience Design': '3',
        'Bachelor of Arts Honours in Design': '6',  // Fixed: was '7', now '6'
        'Bachelor of Arts in Digital Marketing and Communication': '8',  // Fixed: was '9', now '8'
        'Bachelor of Design in Interior Design': '11',
        'Diploma in Graphic Design': '12',
        'Diploma in Interior Design': '13',
        'Higher Certificate in Architectural Technology': '22',
        'Higher Certificate in Design Techniques': '24',
        'Higher Certificate in Fashion Design': '25'
    };

    /**
     * Selects a qualification from the CSV data
     * @returns Promise<void>
     */
    async randomLateQualification(): Promise<void> {
        try {
            await this.loadCSVData();
            const combination = this.getCombination();
            const dropdown = this.page.locator('#c4');
            
            const value = this.qualificationMapping[combination.Qualification];
            if (!value) {
                throw new Error(`No mapping found for qualification: ${combination.Qualification}`);
            }

            await dropdown.selectOption({ value });
            console.log(`Selected Qualification: ${combination.Qualification} (value: ${value})`);
            await this.page.waitForTimeout(2000);
        } catch (error) {
            console.error('Error in randomLateQualification:', error);
            throw error;
        }
    }

    /**
     * Selects an academic year from the CSV data
     * @returns Promise<void>
     */
    async randomAcademicYear(): Promise<void> {
        try {
            await this.loadCSVData();
            const combination = this.getCombination();
            const dropdown = this.page.locator('#c5');
            
            // Academic Year dropdown values: 0=1, 1=2, 2=3, 3=4
            // Subtract 1 from CSV value to get the correct dropdown value
            const academicYearValue = (parseInt(combination['Academic Year']) - 1).toString();
            await dropdown.selectOption({ value: academicYearValue });
            await this.page.waitForTimeout(2000);
            console.log(`Selected Academic Year: ${combination['Academic Year']} (value: ${academicYearValue})`);
        } catch (error) {
            console.error('Error in randomAcademicYear:', error);
            throw error;
        }
    }

    /**
     * Selects a calendar year from the CSV data
     * @returns Promise<void>
     */
    async selectCalendarYear(): Promise<void> {
        try {
            // Always select 2025 (value: 0) since that's what the CSV data specifies
            // HTML: <option value="0">2025</option>
            await this.page.selectOption('#c6', { value: '0' });
            await this.page.waitForTimeout(2000);
            console.log('Selected Calendar Year 2025 (value: 0)');
        } catch (error) {
            console.error('Error in selectCalendarYear:', error);
            throw error;
        }
    }
}

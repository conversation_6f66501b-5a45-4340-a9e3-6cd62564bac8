/**
 * Finance module locators
 * Contains all selectors and locators for the Finance page
 */

export const financeLocators = {
  // Navigation
  navigation: {
    financesMenu: 'text=Finances',
    financialOverviewLink: 'text=Financial Overview',
    debtorsAccountsLink: 'text=Debtors Accounts',
    bankImportLink: 'text=Bank Import',
    debitOrdersLink: 'text=Debit Orders',
    ageAnalysisLink: 'text=Age Analysis'
  },

  // Date range inputs
  dateRange: {
    fromDateInput: 'input#c51',
    toDateInput: 'input#c52',
    applyButton: 'button#c57',
    startDateInput: 'input[aria-label="Start Date"]',
    endDateInput: 'input[aria-label="End Date"]'
  },

  // Data grid
  dataGrid: {
    mainDataGrid: '#c9_dataGrid'
  },

  // Export functionality
  export: {
    exportButton: 'button#c58',
    progressModal: 'text=Progress',
    okButton: 'button:has-text("OK")'
  },

  // Debtors functionality
  debtors: {
    futureDebtorSlider: '.slider',
    applyFiltersButton: 'button[name="Apply filters"]',
    addDebtorsAccountButton: 'button[name="Add Debtors Account"]',
    saveButton: 'button#c25',
    debtorsAccountHeading: 'heading[name="Debtors Account"]'
  },

  // Bank import
  bankImport: {
    importButton: 'form#BankTransaction_OverviewForm > div.row > div > div.row > div > span > button[name="c5"]',
    confirmButton: 'button[name="Confirm"]',
    csvImportModal: 'h4#importModalLabel:has-text("Import Bank Transactions")'
  },

  // Debit orders
  debitOrders: {
    addSubmissionButton: 'div#debitOrderSubmission button[name="c2"]',
    selectCurrencyText: 'div:has-text("Please Select Currency")',
    currencyModal: '.swal-modal[role="dialog"]',
    campusDropdown: 'text=--Please Select--',
    campusCheckboxes: 'checkbox'
  },

  // Age analysis
  ageAnalysis: {
    generateReportButton: 'button[name="Generate Report"]',
    progressBar: '#c10 .progress-bar-success',
    dataTable: 'table tbody'
  },

  // Messages and modals
  messages: {
    swalContent: '.swal-content p',
    errorDialog: 'dialog',
    errorTitle: 'text=Oh No!',
    campusErrorMessage: 'text=Please provide at least one campus to generate report.',
    dateRangeErrorMessage: 'Please select a date range filter.',
    linkIndividualErrorMessage: 'Please find and select an individual to link'
  }
};

/**
 * Finance page role-based locators for better accessibility
 */
export const financeRoleLocators = {
  applyFiltersButton: { role: 'button', name: 'Apply filters' },
  addDebtorsAccountButton: { role: 'button', name: 'Add Debtors Account' },
  confirmButton: { role: 'button', name: 'Confirm' },
  generateReportButton: { role: 'button', name: 'Generate Report' },
  debtorsAccountHeading: { role: 'heading', name: 'Debtors Account' },
  startDateLabel: { label: 'Start Date' },
  endDateLabel: { label: 'End Date' },
  errorDialog: { role: 'dialog' },
  okButton: { role: 'button', name: 'OK' },
  dataTable: { role: 'table' },
  campusCheckbox: { role: 'checkbox' }
};

/**
 * Month name to number mapping for date formatting
 */
export const monthMapping: { [key: string]: string } = {
  'Jan': '01', 'Feb': '02', 'Mar': '03', 'Apr': '04',
  'May': '05', 'Jun': '06', 'Jul': '07', 'Aug': '08',
  'Sep': '09', 'Oct': '10', 'Nov': '11', 'Dec': '12'
};

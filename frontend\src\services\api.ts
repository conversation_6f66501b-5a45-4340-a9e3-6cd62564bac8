import axios from 'axios'
import type { TestSuite, Test, TestExecution, SystemStats, PlaywrightConfig } from '@/types'

const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`)
    return config
  },
  (error) => {
    console.error('API Request Error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.url}`)
    return response
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// Test Suites API
export const testSuitesApi = {
  getAll: () => api.get<TestSuite[]>('/tests/suites').then(res => res.data),
  getById: (id: string) => api.get<TestSuite>(`/tests/suites/${id}`).then(res => res.data),
  refresh: () => api.post('/tests/refresh').then(res => res.data),
}

// Tests API
export const testsApi = {
  getAll: (filters?: { project?: string; suite?: string; status?: string }) => 
    api.get<Test[]>('/tests', { params: filters }).then(res => res.data),
  getById: (id: string) => api.get<Test>(`/tests/${id}`).then(res => res.data),
  search: (query: string, filters?: { project?: string; suite?: string }) =>
    api.get<Test[]>('/tests/search', { params: { q: query, ...filters } }).then(res => res.data),
  getStats: () => api.get<SystemStats>('/tests/stats').then(res => res.data),
}

// Execution API
export const executionApi = {
  start: (params: {
    tests?: string[]
    suites?: string[]
    projects?: string[]
    command?: string
    options?: any
  }) => api.post<TestExecution>('/execution/start', params).then(res => res.data),
  
  getAll: (filters?: { status?: string; limit?: number; offset?: number }) =>
    api.get<TestExecution[]>('/execution', { params: filters }).then(res => res.data),
  
  getById: (id: string) => api.get<TestExecution>(`/execution/${id}`).then(res => res.data),
  
  cancel: (id: string) => api.post(`/execution/${id}/cancel`).then(res => res.data),
  
  getLogs: (id: string, since?: Date) =>
    api.get(`/execution/${id}/logs`, { 
      params: since ? { since: since.toISOString() } : {} 
    }).then(res => res.data),
  
  getActive: () => api.get<TestExecution[]>('/execution/active').then(res => res.data),
  
  getQueue: () => api.get<TestExecution[]>('/execution/queue').then(res => res.data),
}

// Reports API
export const reportsApi = {
  getAllure: () => api.get('/reports/allure').then(res => res.data),
  getPlaywright: () => api.get('/reports/playwright', { responseType: 'text' }).then(res => res.data),
  getLogs: () => api.get('/reports/logs').then(res => res.data),
  getLogFile: (filename: string) => 
    api.get(`/reports/logs/${filename}`, { responseType: 'text' }).then(res => res.data),
  getCoverage: () => api.get('/reports/coverage').then(res => res.data),
}

// Config API
export const configApi = {
  getPlaywright: () => api.get<PlaywrightConfig>('/config/playwright').then(res => res.data),
  getSystem: () => api.get('/config/system').then(res => res.data),
  getProjects: () => api.get('/config/projects').then(res => res.data),
}

// Health check
export const healthApi = {
  check: () => api.get('/health').then(res => res.data),
}

export default api

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Search, Filter, RefreshCw, Play, FileText } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { testSuitesApi, testsApi } from '@/services/api'
import { useSocketStore } from '@/stores/socketStore'
import { cn, getStatusColor } from '@/lib/utils'
import type { TestSuite, Test } from '@/types'

export function TestSuites() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedProject, setSelectedProject] = useState<string>('')
  const [selectedSuite, setSelectedSuite] = useState<TestSuite | null>(null)
  const { startExecution } = useSocketStore()

  const { data: suites = [], isLoading: suitesLoading, refetch: refetchSuites } = useQuery({
    queryKey: ['test-suites'],
    queryFn: testSuitesApi.getAll,
  })

  const { data: tests = [], isLoading: testsLoading } = useQuery({
    queryKey: ['tests', selectedProject, selectedSuite?.name],
    queryFn: () => testsApi.getAll({
      project: selectedProject || undefined,
      suite: selectedSuite?.name || undefined,
    }),
  })

  const filteredSuites = suites.filter(suite => {
    const matchesSearch = suite.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         suite.description?.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesProject = !selectedProject || suite.project === selectedProject
    return matchesSearch && matchesProject
  })

  const projects = Array.from(new Set(suites.map(suite => suite.project)))

  const handleRunSuite = (suite: TestSuite) => {
    startExecution({
      suites: [suite.id],
      projects: [suite.project]
    })
  }

  const handleRunTest = (test: Test) => {
    startExecution({
      tests: [test.id],
      projects: [test.project]
    })
  }

  const handleRefresh = async () => {
    await testSuitesApi.refresh()
    refetchSuites()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Test Suites</h1>
          <p className="text-muted-foreground">
            Browse and manage your Playwright test suites
          </p>
        </div>
        <Button onClick={handleRefresh} disabled={suitesLoading}>
          <RefreshCw className={cn("mr-2 h-4 w-4", suitesLoading && "animate-spin")} />
          Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                type="text"
                placeholder="Search test suites..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
            
            <select
              value={selectedProject}
              onChange={(e) => setSelectedProject(e.target.value)}
              className="px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="">All Projects</option>
              {projects.map(project => (
                <option key={project} value={project}>{project}</option>
              ))}
            </select>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Suites List */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Test Suites ({filteredSuites.length})</h2>
          
          {suitesLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-muted rounded w-3/4"></div>
                    <div className="h-3 bg-muted rounded w-1/2"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="h-3 bg-muted rounded w-full"></div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {filteredSuites.map(suite => (
                <Card 
                  key={suite.id}
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    selectedSuite?.id === suite.id && "ring-2 ring-primary"
                  )}
                  onClick={() => setSelectedSuite(suite)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg">{suite.name}</CardTitle>
                        <CardDescription className="mt-1">
                          {suite.description || 'No description available'}
                        </CardDescription>
                      </div>
                      <Button
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleRunSuite(suite)
                        }}
                      >
                        <Play className="h-4 w-4 mr-1" />
                        Run
                      </Button>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center space-x-4">
                        <Badge variant="outline">{suite.project}</Badge>
                        <span className="text-muted-foreground">
                          {suite.tests.length} tests
                        </span>
                        {suite.status && (
                          <Badge className={getStatusColor(suite.status)}>
                            {suite.status}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2 text-muted-foreground">
                        <FileText className="h-4 w-4" />
                        <span>{suite.filePath}</span>
                      </div>
                    </div>
                    
                    {suite.tags && suite.tags.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {suite.tags.map(tag => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Test Details */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">
            {selectedSuite ? `Tests in ${selectedSuite.name}` : 'Select a test suite'}
          </h2>
          
          {selectedSuite ? (
            <div className="space-y-4">
              {testsLoading ? (
                <div className="space-y-3">
                  {[...Array(5)].map((_, i) => (
                    <Card key={i} className="animate-pulse">
                      <CardContent className="p-4">
                        <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                        <div className="h-3 bg-muted rounded w-1/2"></div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="space-y-3">
                  {tests
                    .filter(test => test.suite === selectedSuite.name)
                    .map(test => (
                    <Card key={test.id} className="hover:shadow-sm transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="font-medium">{test.name}</h4>
                            {test.description && (
                              <p className="text-sm text-muted-foreground mt-1">
                                {test.description}
                              </p>
                            )}
                            
                            <div className="flex items-center space-x-3 mt-2 text-sm">
                              <span className="text-muted-foreground">
                                Line {test.line}
                              </span>
                              {test.severity && (
                                <Badge variant="outline" className="text-xs">
                                  {test.severity}
                                </Badge>
                              )}
                              {test.status && (
                                <Badge className={getStatusColor(test.status)}>
                                  {test.status}
                                </Badge>
                              )}
                            </div>
                            
                            {test.tags && test.tags.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-2">
                                {test.tags.map(tag => (
                                  <Badge key={tag} variant="secondary" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                          
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleRunTest(test)}
                          >
                            <Play className="h-3 w-3 mr-1" />
                            Run
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="p-8 text-center">
                <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">
                  Select a test suite from the left to view its tests
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}

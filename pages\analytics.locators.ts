/**
 * Analytics module locators
 * Contains all selectors and locators for the Analytics page
 */

export const analyticsLocators = {
  // Navigation
  navigation: {
    systemConfigurationDropdown: 'div#SystemConfiguration_dropdown',
    reportManagerLink: '.side-menu-link >> text=Report Manager',
    dataAnalyticsLink: '#LeftSideBar div'
  },

  // Report management
  reports: {
    dataGrid: '//div[@id="ReportsDataGridObj-grid"]//table/tbody/tr',
    dataGridRows: 'tr.ReportsDataGridObj-row:not(.hidden)',
    searchBar: '//*[@id="ReportsDataGridObj-jdgrid-search"]',
    applyFilterButton: '//*[@id="ReportsDataGridObj-jdgrid-apply-filter"]',
    tableBody: '//tbody'
  },

  // Buttons
  buttons: {
    newCategory: 'button[name="New Category"]',
    newReport: 'button[name="New Report"]'
  },

  // Form inputs
  inputs: {
    reportCategoryName: 'input[aria-label="Report Category Name"]',
    reportName: 'input[aria-label="Report Name"]'
  }
};

/**
 * Analytics page role-based locators for better accessibility
 */
export const analyticsRoleLocators = {
  newCategoryButton: { role: 'button', name: 'New Report Category' },
  newReportButton: { role: 'button', name: 'New Report', exact: true },
  reportCategoryNameInput: { label: 'Report Category Name' },
  reportNameInput: { label: 'Report Name' }
};

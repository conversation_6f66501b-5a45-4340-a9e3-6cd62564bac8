import { spawn, ChildProcess } from 'child_process'
import { v4 as uuidv4 } from 'uuid'
import { Server } from 'socket.io'
import { logger } from '../utils/logger.js'
import type { TestExecution, ExecutionOptions, LogEntry } from '../types/index.js'

export class TestExecutionService {
  private executions: Map<string, TestExecution> = new Map()
  private activeProcesses: Map<string, ChildProcess> = new Map()
  private executionQueue: TestExecution[] = []
  private maxConcurrentExecutions = 2
  private io: Server

  constructor(io: Server) {
    this.io = io
    this.startQueueProcessor()
  }

  async startExecution(params: {
    tests?: string[]
    suites?: string[]
    projects?: string[]
    command?: string
    options?: ExecutionOptions
  }): Promise<TestExecution> {
    const executionId = uuidv4()
    const command = this.buildPlaywrightCommand(params)
    
    const execution: TestExecution = {
      id: executionId,
      project: params.projects?.[0] || 'default',
      status: 'queued',
      startTime: new Date(),
      output: [],
      command,
      options: params.options
    }

    this.executions.set(executionId, execution)
    this.executionQueue.push(execution)

    logger.info(`Test execution queued: ${executionId}`, { command })
    
    // Notify clients
    this.io.emit('execution-queued', execution)
    
    return execution
  }

  private buildPlaywrightCommand(params: {
    tests?: string[]
    suites?: string[]
    projects?: string[]
    command?: string
    options?: ExecutionOptions
  }): string {
    if (params.command) {
      return params.command
    }

    let cmd = 'npx playwright test'
    
    // Add project filter
    if (params.projects && params.projects.length > 0) {
      cmd += ` --project=${params.projects.join(',')}`
    }
    
    // Add specific test files
    if (params.tests && params.tests.length > 0) {
      // Convert test IDs to file paths
      const testFiles = params.tests
        .map(testId => testId.split(':')[0])
        .filter((file, index, arr) => arr.indexOf(file) === index) // Remove duplicates
      cmd += ` ${testFiles.join(' ')}`
    }
    
    // Add options
    if (params.options) {
      if (params.options.headless === false) {
        cmd += ' --headed'
      }
      if (params.options.workers) {
        cmd += ` --workers=${params.options.workers}`
      }
      if (params.options.retries) {
        cmd += ` --retries=${params.options.retries}`
      }
      if (params.options.timeout) {
        cmd += ` --timeout=${params.options.timeout}`
      }
      if (params.options.grep) {
        cmd += ` --grep="${params.options.grep}"`
      }
    }
    
    return cmd
  }

  private startQueueProcessor(): void {
    setInterval(() => {
      this.processQueue()
    }, 1000) // Check queue every second
  }

  private async processQueue(): Promise<void> {
    const activeCount = this.activeProcesses.size
    
    if (activeCount >= this.maxConcurrentExecutions || this.executionQueue.length === 0) {
      return
    }

    const execution = this.executionQueue.shift()
    if (!execution) return

    await this.executeTest(execution)
  }

  private async executeTest(execution: TestExecution): Promise<void> {
    try {
      execution.status = 'running'
      execution.startTime = new Date()
      this.executions.set(execution.id, execution)

      logger.info(`Starting test execution: ${execution.id}`, { command: execution.command })
      
      // Notify clients
      this.io.emit('execution-started', execution)
      this.io.to(`execution-${execution.id}`).emit('execution-update', execution)

      const args = execution.command.split(' ').slice(1) // Remove 'npx'
      const process = spawn('npx', args, {
        cwd: process.cwd() + '/..',
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, CI: 'true' }
      })

      this.activeProcesses.set(execution.id, process)

      // Handle stdout
      process.stdout?.on('data', (data: Buffer) => {
        const output = data.toString()
        execution.output.push(output)
        
        const logEntry: LogEntry = {
          timestamp: new Date(),
          level: 'info',
          message: output.trim(),
          executionId: execution.id
        }
        
        this.io.to(`execution-${execution.id}`).emit('execution-log', logEntry)
        logger.info(`Execution ${execution.id} output:`, output.trim())
      })

      // Handle stderr
      process.stderr?.on('data', (data: Buffer) => {
        const output = data.toString()
        execution.output.push(output)
        
        const logEntry: LogEntry = {
          timestamp: new Date(),
          level: 'error',
          message: output.trim(),
          executionId: execution.id
        }
        
        this.io.to(`execution-${execution.id}`).emit('execution-log', logEntry)
        logger.error(`Execution ${execution.id} error:`, output.trim())
      })

      // Handle process completion
      process.on('close', (code: number | null) => {
        execution.endTime = new Date()
        execution.duration = execution.endTime.getTime() - execution.startTime.getTime()
        execution.status = code === 0 ? 'completed' : 'failed'
        
        this.executions.set(execution.id, execution)
        this.activeProcesses.delete(execution.id)
        
        logger.info(`Test execution completed: ${execution.id}`, { 
          code, 
          duration: execution.duration,
          status: execution.status 
        })
        
        // Notify clients
        this.io.emit('execution-completed', execution)
        this.io.to(`execution-${execution.id}`).emit('execution-update', execution)
      })

      // Handle process errors
      process.on('error', (error: Error) => {
        execution.endTime = new Date()
        execution.duration = execution.endTime.getTime() - execution.startTime.getTime()
        execution.status = 'failed'
        execution.output.push(`Process error: ${error.message}`)
        
        this.executions.set(execution.id, execution)
        this.activeProcesses.delete(execution.id)
        
        logger.error(`Test execution failed: ${execution.id}`, error)
        
        // Notify clients
        this.io.emit('execution-failed', execution)
        this.io.to(`execution-${execution.id}`).emit('execution-update', execution)
      })

    } catch (error) {
      execution.status = 'failed'
      execution.endTime = new Date()
      execution.duration = execution.endTime.getTime() - execution.startTime.getTime()
      execution.output.push(`Execution error: ${error instanceof Error ? error.message : 'Unknown error'}`)
      
      this.executions.set(execution.id, execution)
      this.activeProcesses.delete(execution.id)
      
      logger.error(`Failed to start test execution: ${execution.id}`, error)
      
      // Notify clients
      this.io.emit('execution-failed', execution)
      this.io.to(`execution-${execution.id}`).emit('execution-update', execution)
    }
  }

  async cancelExecution(executionId: string): Promise<void> {
    const execution = this.executions.get(executionId)
    if (!execution) {
      throw new Error('Execution not found')
    }

    const process = this.activeProcesses.get(executionId)
    if (process) {
      process.kill('SIGTERM')
      this.activeProcesses.delete(executionId)
    }

    // Remove from queue if queued
    const queueIndex = this.executionQueue.findIndex(e => e.id === executionId)
    if (queueIndex !== -1) {
      this.executionQueue.splice(queueIndex, 1)
    }

    execution.status = 'cancelled'
    execution.endTime = new Date()
    execution.duration = execution.endTime.getTime() - execution.startTime.getTime()
    
    this.executions.set(executionId, execution)
    
    logger.info(`Test execution cancelled: ${executionId}`)
    
    // Notify clients
    this.io.emit('execution-cancelled', execution)
    this.io.to(`execution-${executionId}`).emit('execution-update', execution)
  }

  async getExecutions(filters?: {
    status?: string
    limit?: number
    offset?: number
  }): Promise<TestExecution[]> {
    let executions = Array.from(this.executions.values())
    
    if (filters?.status) {
      executions = executions.filter(e => e.status === filters.status)
    }
    
    // Sort by start time (most recent first)
    executions.sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
    
    if (filters?.offset) {
      executions = executions.slice(filters.offset)
    }
    
    if (filters?.limit) {
      executions = executions.slice(0, filters.limit)
    }
    
    return executions
  }

  async getExecution(executionId: string): Promise<TestExecution | null> {
    return this.executions.get(executionId) || null
  }

  async getExecutionLogs(executionId: string, options?: {
    since?: Date
  }): Promise<LogEntry[]> {
    const execution = this.executions.get(executionId)
    if (!execution) {
      throw new Error('Execution not found')
    }

    // Convert output array to log entries
    const logs: LogEntry[] = execution.output.map((output, index) => ({
      timestamp: new Date(execution.startTime.getTime() + (index * 100)), // Approximate timestamps
      level: output.toLowerCase().includes('error') ? 'error' : 'info',
      message: output.trim(),
      executionId
    }))

    if (options?.since) {
      return logs.filter(log => log.timestamp >= options.since!)
    }

    return logs
  }

  getActiveExecutions(): TestExecution[] {
    return Array.from(this.executions.values()).filter(e => e.status === 'running')
  }

  getQueuedExecutions(): TestExecution[] {
    return this.executionQueue.slice()
  }
}

import { Router } from 'express'
import { logger } from '../utils/logger.js'
import type { TestDiscoveryService } from '../services/testDiscovery.js'

export const testRoutes = Router()

// Get all test suites
testRoutes.get('/suites', async (req, res) => {
  try {
    const testDiscovery: TestDiscoveryService = req.app.locals.testDiscovery
    const suites = await testDiscovery.getAllSuites()
    res.json(suites)
  } catch (error) {
    logger.error('Failed to get test suites:', error)
    res.status(500).json({
      error: 'Failed to retrieve test suites',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get specific test suite
testRoutes.get('/suites/:id', async (req, res) => {
  try {
    const testDiscovery: TestDiscoveryService = req.app.locals.testDiscovery
    const suite = await testDiscovery.getSuite(req.params.id)
    
    if (!suite) {
      return res.status(404).json({ error: 'Test suite not found' })
    }
    
    res.json(suite)
  } catch (error) {
    logger.error('Failed to get test suite:', error)
    res.status(500).json({
      error: 'Failed to retrieve test suite',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get all tests
testRoutes.get('/', async (req, res) => {
  try {
    const testDiscovery: TestDiscoveryService = req.app.locals.testDiscovery
    const { project, suite, status } = req.query
    
    const filters = {
      project: project as string,
      suite: suite as string,
      status: status as string
    }
    
    const tests = await testDiscovery.getAllTests(filters)
    res.json(tests)
  } catch (error) {
    logger.error('Failed to get tests:', error)
    res.status(500).json({
      error: 'Failed to retrieve tests',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get specific test
testRoutes.get('/:id', async (req, res) => {
  try {
    const testDiscovery: TestDiscoveryService = req.app.locals.testDiscovery
    const test = await testDiscovery.getTest(req.params.id)
    
    if (!test) {
      return res.status(404).json({ error: 'Test not found' })
    }
    
    res.json(test)
  } catch (error) {
    logger.error('Failed to get test:', error)
    res.status(500).json({
      error: 'Failed to retrieve test',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Search tests
testRoutes.get('/search', async (req, res) => {
  try {
    const testDiscovery: TestDiscoveryService = req.app.locals.testDiscovery
    const { q, project, suite } = req.query
    
    if (!q || typeof q !== 'string') {
      return res.status(400).json({ error: 'Search query is required' })
    }
    
    const results = await testDiscovery.searchTests(q, {
      project: project as string,
      suite: suite as string
    })
    
    res.json(results)
  } catch (error) {
    logger.error('Failed to search tests:', error)
    res.status(500).json({
      error: 'Failed to search tests',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Refresh test discovery
testRoutes.post('/refresh', async (req, res) => {
  try {
    const testDiscovery: TestDiscoveryService = req.app.locals.testDiscovery
    await testDiscovery.refresh()
    res.json({ message: 'Test discovery refreshed successfully' })
  } catch (error) {
    logger.error('Failed to refresh test discovery:', error)
    res.status(500).json({
      error: 'Failed to refresh test discovery',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

// Get test statistics
testRoutes.get('/stats', async (req, res) => {
  try {
    const testDiscovery: TestDiscoveryService = req.app.locals.testDiscovery
    const stats = await testDiscovery.getStats()
    res.json(stats)
  } catch (error) {
    logger.error('Failed to get test statistics:', error)
    res.status(500).json({
      error: 'Failed to retrieve test statistics',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
})

import { create } from 'zustand'
import { io, Socket } from 'socket.io-client'
import toast from 'react-hot-toast'
import type { TestExecution, LogEntry } from '@/types'

interface SocketState {
  socket: Socket | null
  isConnected: boolean
  activeExecutions: TestExecution[]
  executionLogs: Map<string, LogEntry[]>
  connect: () => void
  disconnect: () => void
  joinExecution: (executionId: string) => void
  leaveExecution: (executionId: string) => void
  joinDashboard: () => void
  leaveDashboard: () => void
  startExecution: (params: {
    tests?: string[]
    suites?: string[]
    projects?: string[]
    command?: string
  }) => void
  cancelExecution: (executionId: string) => void
}

export const useSocketStore = create<SocketState>((set, get) => ({
  socket: null,
  isConnected: false,
  activeExecutions: [],
  executionLogs: new Map(),

  connect: () => {
    const socket = io('http://localhost:3001', {
      transports: ['websocket'],
    })

    socket.on('connect', () => {
      console.log('Connected to server')
      set({ isConnected: true })
      toast.success('Connected to test server')
    })

    socket.on('disconnect', (reason) => {
      console.log('Disconnected from server:', reason)
      set({ isConnected: false })
      toast.error('Disconnected from test server')
    })

    socket.on('connected', (data) => {
      console.log('Connection confirmed:', data)
    })

    socket.on('execution-queued', (execution: TestExecution) => {
      console.log('Execution queued:', execution)
      toast.success(`Test execution queued: ${execution.id.slice(0, 8)}`)
    })

    socket.on('execution-started', (execution: TestExecution) => {
      console.log('Execution started:', execution)
      set(state => ({
        activeExecutions: [...state.activeExecutions, execution]
      }))
      toast.success(`Test execution started: ${execution.id.slice(0, 8)}`)
    })

    socket.on('execution-completed', (execution: TestExecution) => {
      console.log('Execution completed:', execution)
      set(state => ({
        activeExecutions: state.activeExecutions.filter(e => e.id !== execution.id)
      }))
      toast.success(`Test execution completed: ${execution.id.slice(0, 8)}`)
    })

    socket.on('execution-failed', (execution: TestExecution) => {
      console.log('Execution failed:', execution)
      set(state => ({
        activeExecutions: state.activeExecutions.filter(e => e.id !== execution.id)
      }))
      toast.error(`Test execution failed: ${execution.id.slice(0, 8)}`)
    })

    socket.on('execution-cancelled', (execution: TestExecution) => {
      console.log('Execution cancelled:', execution)
      set(state => ({
        activeExecutions: state.activeExecutions.filter(e => e.id !== execution.id)
      }))
      toast.info(`Test execution cancelled: ${execution.id.slice(0, 8)}`)
    })

    socket.on('execution-update', (execution: TestExecution) => {
      console.log('Execution update:', execution)
      set(state => ({
        activeExecutions: state.activeExecutions.map(e => 
          e.id === execution.id ? execution : e
        )
      }))
    })

    socket.on('execution-log', (logEntry: LogEntry) => {
      console.log('Execution log:', logEntry)
      set(state => {
        const logs = state.executionLogs.get(logEntry.executionId!) || []
        const newLogs = new Map(state.executionLogs)
        newLogs.set(logEntry.executionId!, [...logs, logEntry])
        return { executionLogs: newLogs }
      })
    })

    socket.on('execution-error', (error) => {
      console.error('Execution error:', error)
      toast.error(`Execution error: ${error.error}`)
    })

    socket.on('system-stats', (stats) => {
      console.log('System stats:', stats)
      // Handle system stats updates
    })

    set({ socket })
  },

  disconnect: () => {
    const { socket } = get()
    if (socket) {
      socket.disconnect()
      set({ socket: null, isConnected: false })
    }
  },

  joinExecution: (executionId: string) => {
    const { socket } = get()
    if (socket) {
      socket.emit('join-execution', executionId)
    }
  },

  leaveExecution: (executionId: string) => {
    const { socket } = get()
    if (socket) {
      socket.emit('leave-execution', executionId)
    }
  },

  joinDashboard: () => {
    const { socket } = get()
    if (socket) {
      socket.emit('join-dashboard')
    }
  },

  leaveDashboard: () => {
    const { socket } = get()
    if (socket) {
      socket.emit('leave-dashboard')
    }
  },

  startExecution: (params) => {
    const { socket } = get()
    if (socket) {
      socket.emit('start-test-execution', params)
    }
  },

  cancelExecution: (executionId: string) => {
    const { socket } = get()
    if (socket) {
      socket.emit('cancel-execution', executionId)
    }
  },
}))
